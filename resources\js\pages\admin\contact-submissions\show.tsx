import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, router } from '@inertiajs/react';
import { ArrowLeft, Building2, Calendar, CheckCircle, Mail, Phone, User } from 'lucide-react';

interface ContactSubmission {
    id: number;
    name: string;
    email: string;
    phone?: string;
    subject: string;
    message: string;
    status: string;
    metadata?: {
        ip?: string;
        user_agent?: string;
        submitted_at?: string;
    };
    business_unit: {
        id: number;
        name: string;
        slug: string;
    };
    created_at: string;
    read_at?: string;
    replied_at?: string;
}

interface Props {
    submission: ContactSubmission;
}

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Admin', href: '/admin' },
    { title: '<PERSON><PERSON>', href: '/admin/contact-submissions' },
    { title: 'Detail <PERSON>', href: '#' },
];

export default function ContactSubmissionShow({ submission }: Props) {
    const handleMarkAsReplied = () => {
        if (confirm('Tandai pesan ini sebagai sudah dibalas?')) {
            router.post(route('admin.contact-submissions.mark-replied', submission.id));
        }
    };

    const getStatusBadgeVariant = (status: string) => {
        switch (status) {
            case 'new':
                return 'destructive';
            case 'read':
                return 'default';
            case 'replied':
                return 'secondary';
            default:
                return 'outline';
        }
    };

    const getStatusLabel = (status: string) => {
        switch (status) {
            case 'new':
                return 'Baru';
            case 'read':
                return 'Dibaca';
            case 'replied':
                return 'Dibalas';
            default:
                return status;
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Pesan dari ${submission.name}`} />

            <div className="flex h-full flex-1 flex-col gap-4 p-4">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold tracking-tight">Detail Pesan Kontak</h1>
                        <p className="text-muted-foreground">Pesan dari {submission.name}</p>
                    </div>
                    <div className="flex items-center gap-2">
                        {submission.status !== 'replied' && (
                            <Button onClick={handleMarkAsReplied}>
                                <CheckCircle className="mr-2 h-4 w-4" />
                                Tandai Dibalas
                            </Button>
                        )}
                        <Link href="/admin/contact-submissions">
                            <Button variant="outline">
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Kembali
                            </Button>
                        </Link>
                    </div>
                </div>

                <div className="grid gap-4 md:grid-cols-2">
                    {/* Contact Information */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <User className="h-5 w-5" />
                                Informasi Pengirim
                            </CardTitle>
                            <CardDescription>Data kontak pengirim pesan</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="space-y-2">
                                <div className="flex items-center gap-2">
                                    <User className="h-4 w-4 text-muted-foreground" />
                                    <span className="font-medium">Nama</span>
                                </div>
                                <p className="text-lg">{submission.name}</p>
                            </div>

                            <div className="space-y-2">
                                <div className="flex items-center gap-2">
                                    <Mail className="h-4 w-4 text-muted-foreground" />
                                    <span className="font-medium">Email</span>
                                </div>
                                <p className="text-lg">
                                    <a href={`mailto:${submission.email}`} className="text-blue-600 hover:underline">
                                        {submission.email}
                                    </a>
                                </p>
                            </div>

                            {submission.phone && (
                                <div className="space-y-2">
                                    <div className="flex items-center gap-2">
                                        <Phone className="h-4 w-4 text-muted-foreground" />
                                        <span className="font-medium">Telepon</span>
                                    </div>
                                    <p className="text-lg">
                                        <a href={`tel:${submission.phone}`} className="text-blue-600 hover:underline">
                                            {submission.phone}
                                        </a>
                                    </p>
                                </div>
                            )}

                            <div className="space-y-2">
                                <div className="flex items-center gap-2">
                                    <Building2 className="h-4 w-4 text-muted-foreground" />
                                    <span className="font-medium">Unit Bisnis</span>
                                </div>
                                <p className="text-lg">{submission.business_unit.name}</p>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Status Information */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Calendar className="h-5 w-5" />
                                Status & Waktu
                            </CardTitle>
                            <CardDescription>Informasi status dan timeline pesan</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="space-y-2">
                                <span className="font-medium">Status Pesan</span>
                                <div>
                                    <Badge variant={getStatusBadgeVariant(submission.status)}>{getStatusLabel(submission.status)}</Badge>
                                </div>
                            </div>

                            <div className="space-y-2">
                                <span className="font-medium">Dikirim</span>
                                <p className="text-sm text-muted-foreground">
                                    {new Date(submission.created_at).toLocaleDateString('id-ID', {
                                        year: 'numeric',
                                        month: 'long',
                                        day: 'numeric',
                                        hour: '2-digit',
                                        minute: '2-digit',
                                    })}
                                </p>
                            </div>

                            {submission.read_at && (
                                <div className="space-y-2">
                                    <span className="font-medium">Dibaca</span>
                                    <p className="text-sm text-muted-foreground">
                                        {new Date(submission.read_at).toLocaleDateString('id-ID', {
                                            year: 'numeric',
                                            month: 'long',
                                            day: 'numeric',
                                            hour: '2-digit',
                                            minute: '2-digit',
                                        })}
                                    </p>
                                </div>
                            )}

                            {submission.replied_at && (
                                <div className="space-y-2">
                                    <span className="font-medium">Dibalas</span>
                                    <p className="text-sm text-muted-foreground">
                                        {new Date(submission.replied_at).toLocaleDateString('id-ID', {
                                            year: 'numeric',
                                            month: 'long',
                                            day: 'numeric',
                                            hour: '2-digit',
                                            minute: '2-digit',
                                        })}
                                    </p>
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* Message Content */}
                    <Card className="md:col-span-2">
                        <CardHeader>
                            <CardTitle>Subjek: {submission.subject}</CardTitle>
                            <CardDescription>Isi pesan dari {submission.name}</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="rounded-lg bg-muted p-4">
                                <p className="whitespace-pre-wrap">{submission.message}</p>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Technical Information */}
                    {submission.metadata && (
                        <Card className="md:col-span-2">
                            <CardHeader>
                                <CardTitle className="text-base">Informasi Teknis</CardTitle>
                                <CardDescription>Data teknis untuk keperluan debugging</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="grid gap-4 text-sm md:grid-cols-3">
                                    <div>
                                        <span className="font-medium">ID Pesan:</span>
                                        <p className="text-muted-foreground">#{submission.id}</p>
                                    </div>
                                    {submission.metadata.ip && (
                                        <div>
                                            <span className="font-medium">IP Address:</span>
                                            <p className="text-muted-foreground">{submission.metadata.ip}</p>
                                        </div>
                                    )}
                                    {submission.metadata.user_agent && (
                                        <div className="md:col-span-2">
                                            <span className="font-medium">User Agent:</span>
                                            <p className="text-xs break-all text-muted-foreground">{submission.metadata.user_agent}</p>
                                        </div>
                                    )}
                                </div>
                            </CardContent>
                        </Card>
                    )}
                </div>

                {/* Quick Actions */}
                <Card>
                    <CardHeader>
                        <CardTitle className="text-base">Tindakan Cepat</CardTitle>
                        <CardDescription>Aksi yang dapat dilakukan untuk pesan ini</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="flex gap-2">
                            <a href={`mailto:${submission.email}?subject=Re: ${submission.subject}`}>
                                <Button variant="outline">
                                    <Mail className="mr-2 h-4 w-4" />
                                    Balas via Email
                                </Button>
                            </a>
                            {submission.phone && (
                                <a href={`tel:${submission.phone}`}>
                                    <Button variant="outline">
                                        <Phone className="mr-2 h-4 w-4" />
                                        Telepon
                                    </Button>
                                </a>
                            )}
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
