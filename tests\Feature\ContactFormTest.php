<?php

use App\Models\BusinessUnit;
use App\Models\ContactSubmission;
use Illuminate\Support\Facades\Mail;

beforeEach(function () {
    // Run migrations
    $this->artisan('migrate:fresh');

    // Create a business unit for testing
    $this->businessUnit = BusinessUnit::factory()->create([
        'name' => 'Test Business Unit',
        'slug' => 'test-unit',
        'contact_info' => [
            'email' => '<EMAIL>',
            'phone' => '+***********',
            'address' => 'Test Address',
        ],
    ]);
});

it('can submit a contact form', function () {
    Mail::fake();

    $contactData = [
        'name' => '<PERSON>',
        'email' => '<EMAIL>',
        'phone' => '+***********',
        'subject' => 'Test Subject',
        'message' => 'This is a test message.',
    ];

    $response = $this->post(
        route('business-unit.contact.submit', $this->businessUnit->slug),
        $contactData
    );

    $response->assertRedirect();
    $response->assertSessionHas('success');

    // Check if submission was stored
    $this->assertDatabaseHas('contact_submissions', [
        'business_unit_id' => $this->businessUnit->id,
        'name' => '<PERSON> Doe',
        'email' => '<EMAIL>',
        'subject' => 'Test Subject',
        'status' => 'new',
    ]);

    // Check if email was queued (since it implements ShouldQueue)
    Mail::assertQueued(\App\Mail\ContactFormSubmitted::class);
});

it('validates contact form data', function () {
    $response = $this->post(
        route('business-unit.contact.submit', $this->businessUnit->slug),
        [
            'name' => '',
            'email' => 'invalid-email',
            'subject' => '',
            'message' => '',
        ]
    );

    $response->assertSessionHasErrors(['name', 'email', 'subject', 'message']);
});

it('can display contact page', function () {
    // Create a contact page for the business unit
    $this->businessUnit->pages()->create([
        'type' => 'contact',
        'title' => 'Contact Us',
        'content' => 'Contact us for more information',
        'is_active' => true,
        'order' => 1,
    ]);

    $response = $this->get(route('business-unit.contact', $this->businessUnit->slug));

    $response->assertStatus(200);
    $response->assertInertia(fn ($page) => $page->component('public/business-unit/contact'));
});

it('returns 404 if contact page does not exist', function () {
    $response = $this->get(route('business-unit.contact', $this->businessUnit->slug));

    $response->assertStatus(404);
});

it('stores metadata with contact submission', function () {
    Mail::fake();

    $contactData = [
        'name' => 'Jane Doe',
        'email' => '<EMAIL>',
        'subject' => 'Test Subject',
        'message' => 'Test message',
    ];

    $response = $this->post(
        route('business-unit.contact.submit', $this->businessUnit->slug),
        $contactData
    );

    $submission = ContactSubmission::first();

    expect($submission->metadata)->toHaveKey('ip');
    expect($submission->metadata)->toHaveKey('user_agent');
    expect($submission->metadata)->toHaveKey('submitted_at');
});

it('can mark submission as read', function () {
    $submission = ContactSubmission::factory()->create([
        'business_unit_id' => $this->businessUnit->id,
        'status' => 'new',
    ]);

    $submission->markAsRead();

    expect($submission->fresh()->status)->toBe('read');
    expect($submission->fresh()->read_at)->not->toBeNull();
});

it('can mark submission as replied', function () {
    $submission = ContactSubmission::factory()->create([
        'business_unit_id' => $this->businessUnit->id,
        'status' => 'read',
    ]);

    $submission->markAsReplied();

    expect($submission->fresh()->status)->toBe('replied');
    expect($submission->fresh()->replied_at)->not->toBeNull();
});
