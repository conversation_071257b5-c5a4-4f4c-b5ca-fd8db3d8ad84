import { Button } from '@/components/ui/button';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Head, Link } from '@inertiajs/react';
import { ArrowLeft, Building2, Globe, Mail, MapPin, Phone } from 'lucide-react';

interface BusinessUnit {
    id: number;
    name: string;
    slug: string;
    description: string;
    status: string;
    primary_color?: string;
    contact_info?: {
        phone?: string;
        email?: string;
        address?: string;
    };
    social_media?: {
        instagram?: string;
        facebook?: string;
    };
}

interface BusinessUnitPage {
    id: number;
    type: string;
    title: string;
    slug: string;
    content: string;
    meta_data?: Record<string, unknown>;
    seo_data?: {
        title?: string;
        description?: string;
        keywords?: string;
    };
    is_active: boolean;
    order: number;
}

interface Props {
    business_unit: BusinessUnit;
    page: BusinessUnitPage;
    pages: BusinessUnitPage[];
}

export default function BusinessUnitAbout({ business_unit, page, pages }: Props) {
    return (
        <>
            <Head title={page.seo_data?.title || page.title}>
                <meta name="description" content={page.seo_data?.description || business_unit.description} />
                {page.seo_data?.keywords && <meta name="keywords" content={page.seo_data.keywords} />}
            </Head>

            <div className="min-h-screen bg-gray-50">
                {/* Header */}
                <header className="bg-white shadow-sm">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="flex items-center justify-between py-6">
                            <div className="flex items-center">
                                <Link href="/" className="flex items-center">
                                    <Building2 className="mr-2 h-6 w-6 text-blue-600" />
                                    <span className="text-lg font-semibold text-gray-900">Cigi Global</span>
                                </Link>
                                <span className="mx-3 text-gray-400">/</span>
                                <Link href={`/${business_unit.slug}`} className="flex items-center">
                                    {business_unit.primary_color && (
                                        <div className="mr-2 h-4 w-4 rounded-full" style={{ backgroundColor: business_unit.primary_color }} />
                                    )}
                                    <span className="text-lg font-semibold text-gray-900">{business_unit.name}</span>
                                </Link>
                                <span className="mx-3 text-gray-400">/</span>
                                <span className="text-lg text-gray-600">Tentang</span>
                            </div>
                            <nav className="hidden space-x-6 md:flex">
                                <Link href={`/${business_unit.slug}`} className="text-gray-700 hover:text-blue-600">
                                    Beranda
                                </Link>
                                {pages
                                    .filter((p) => p.type !== 'hero' && p.type !== 'about')
                                    .map((navPage) => (
                                        <Link
                                            key={navPage.id}
                                            href={`/${business_unit.slug}/${navPage.slug}`}
                                            className="text-gray-700 hover:text-blue-600"
                                        >
                                            {navPage.title}
                                        </Link>
                                    ))}
                            </nav>
                        </div>
                    </div>
                </header>

                {/* Hero Section */}
                <section
                    className="py-16 text-white"
                    style={{
                        backgroundColor: business_unit.primary_color || '#3B82F6',
                        background: `linear-gradient(135deg, ${business_unit.primary_color || '#3B82F6'}, ${business_unit.primary_color ? business_unit.primary_color + '80' : '#1E40AF'})`,
                    }}
                >
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="mb-6 flex items-center">
                            <Link href={`/${business_unit.slug}`}>
                                <Button variant="secondary" size="sm">
                                    <ArrowLeft className="mr-2 h-4 w-4" />
                                    Kembali
                                </Button>
                            </Link>
                        </div>
                        <h1 className="mb-4 text-4xl font-bold md:text-5xl">{page.title}</h1>
                        <p className="max-w-3xl text-xl opacity-90">Pelajari lebih lanjut tentang {business_unit.name} dan komitmen kami</p>
                    </div>
                </section>

                {/* Content Section */}
                <section className="bg-white py-16">
                    <div className="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
                        <div className="prose prose-lg max-w-none">
                            <div dangerouslySetInnerHTML={{ __html: page.content }} className="leading-relaxed text-gray-700" />
                        </div>
                    </div>
                </section>

                {/* Contact Info Section */}
                {business_unit.contact_info && (
                    <section className="bg-gray-50 py-16">
                        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                            <h2 className="mb-8 text-center text-3xl font-bold text-gray-900">Hubungi Kami</h2>
                            <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
                                {/* Contact Information */}
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Informasi Kontak</CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        {business_unit.contact_info.phone && (
                                            <div className="flex items-center gap-3">
                                                <Phone className="h-5 w-5 text-muted-foreground" />
                                                <div>
                                                    <p className="font-medium">Telepon</p>
                                                    <p className="text-sm text-muted-foreground">{business_unit.contact_info.phone}</p>
                                                </div>
                                            </div>
                                        )}
                                        {business_unit.contact_info.email && (
                                            <div className="flex items-center gap-3">
                                                <Mail className="h-5 w-5 text-muted-foreground" />
                                                <div>
                                                    <p className="font-medium">Email</p>
                                                    <p className="text-sm text-muted-foreground">{business_unit.contact_info.email}</p>
                                                </div>
                                            </div>
                                        )}
                                        {business_unit.contact_info.address && (
                                            <div className="flex items-start gap-3">
                                                <MapPin className="mt-1 h-5 w-5 text-muted-foreground" />
                                                <div>
                                                    <p className="font-medium">Alamat</p>
                                                    <p className="text-sm text-muted-foreground">{business_unit.contact_info.address}</p>
                                                </div>
                                            </div>
                                        )}
                                    </CardContent>
                                </Card>

                                {/* Social Media */}
                                {business_unit.social_media && (
                                    <Card>
                                        <CardHeader>
                                            <CardTitle>Media Sosial</CardTitle>
                                        </CardHeader>
                                        <CardContent className="space-y-4">
                                            {business_unit.social_media.instagram && (
                                                <div className="flex items-center gap-3">
                                                    <Globe className="h-5 w-5 text-muted-foreground" />
                                                    <div>
                                                        <p className="font-medium">Instagram</p>
                                                        <p className="text-sm text-muted-foreground">{business_unit.social_media.instagram}</p>
                                                    </div>
                                                </div>
                                            )}
                                            {business_unit.social_media.facebook && (
                                                <div className="flex items-center gap-3">
                                                    <Globe className="h-5 w-5 text-muted-foreground" />
                                                    <div>
                                                        <p className="font-medium">Facebook</p>
                                                        <p className="text-sm text-muted-foreground">{business_unit.social_media.facebook}</p>
                                                    </div>
                                                </div>
                                            )}
                                        </CardContent>
                                    </Card>
                                )}

                                {/* Quick Actions */}
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Aksi Cepat</CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-3">
                                        <Link href={`/${business_unit.slug}`}>
                                            <Button variant="outline" className="w-full">
                                                Kembali ke Beranda
                                            </Button>
                                        </Link>
                                        {pages.find((p) => p.type === 'contact') && (
                                            <Link href={`/${business_unit.slug}/contact`}>
                                                <Button className="w-full" style={{ backgroundColor: business_unit.primary_color }}>
                                                    Hubungi Kami
                                                </Button>
                                            </Link>
                                        )}
                                        {pages.find((p) => p.type === 'pricing') && (
                                            <Link href={`/${business_unit.slug}/pricing`}>
                                                <Button variant="outline" className="w-full">
                                                    Lihat Harga
                                                </Button>
                                            </Link>
                                        )}
                                    </CardContent>
                                </Card>
                            </div>
                        </div>
                    </section>
                )}

                {/* Footer */}
                <footer className="bg-gray-900 py-12 text-white">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="text-center">
                            <div className="mb-4 flex items-center justify-center">
                                {business_unit.primary_color && (
                                    <div className="mr-3 h-6 w-6 rounded-full" style={{ backgroundColor: business_unit.primary_color }} />
                                )}
                                <h3 className="text-xl font-bold">{business_unit.name}</h3>
                            </div>
                            <p className="mx-auto mb-6 max-w-2xl text-gray-400">{business_unit.description}</p>
                            <div className="flex justify-center space-x-6">
                                <Link href="/" className="text-gray-400 hover:text-white">
                                    Kembali ke Cigi Global
                                </Link>
                                <Link href={`/${business_unit.slug}`} className="text-gray-400 hover:text-white">
                                    Beranda {business_unit.name}
                                </Link>
                            </div>
                        </div>
                    </div>
                </footer>
            </div>
        </>
    );
}
