<?php

use App\Models\BusinessUnit;
use App\Models\User;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

beforeEach(function () {
    // Run migrations
    $this->artisan('migrate:fresh');
    
    // Create permissions and roles
    Permission::create(['name' => 'view galleries']);
    Permission::create(['name' => 'upload galleries']);
    Permission::create(['name' => 'delete galleries']);
    Permission::create(['name' => 'edit galleries']);
    
    $adminRole = Role::create(['name' => 'admin']);
    $adminRole->givePermissionTo(['view galleries', 'upload galleries', 'delete galleries', 'edit galleries']);
    
    // Create admin user
    $this->admin = User::factory()->create();
    $this->admin->assignRole('admin');
    
    // Create business unit
    $this->businessUnit = BusinessUnit::factory()->create();
    
    // Fake storage
    Storage::fake('public');
});

it('allows admin to view gallery index', function () {
    $response = $this->actingAs($this->admin)
        ->get(route('admin.business-units.gallery.index', $this->businessUnit->slug));
    
    $response->assertStatus(200);
    $response->assertInertia(fn ($page) => $page->component('admin/galleries/index'));
});

it('can upload images to gallery', function () {
    $file = UploadedFile::fake()->image('test.jpg', 800, 600);
    
    $response = $this->actingAs($this->admin)
        ->post(route('admin.business-units.gallery.upload', $this->businessUnit->slug), [
            'files' => [$file],
        ]);
    
    $response->assertStatus(200);
    $response->assertJson([
        'message' => 'Gambar berhasil diunggah.',
    ]);
    
    // Check if media was attached to business unit
    expect($this->businessUnit->getMedia('gallery'))->toHaveCount(1);
});

it('validates uploaded files', function () {
    $file = UploadedFile::fake()->create('document.pdf', 1000, 'application/pdf');
    
    $response = $this->actingAs($this->admin)
        ->post(route('admin.business-units.gallery.upload', $this->businessUnit->slug), [
            'files' => [$file],
        ]);
    
    $response->assertSessionHasErrors(['files.0']);
});

it('can delete gallery images', function () {
    // Upload an image first
    $file = UploadedFile::fake()->image('test.jpg');
    $media = $this->businessUnit
        ->addMediaFromRequest('files')
        ->usingFileName($file->getClientOriginalName())
        ->toMediaCollection('gallery');
    
    expect($this->businessUnit->getMedia('gallery'))->toHaveCount(1);
    
    $response = $this->actingAs($this->admin)
        ->delete(route('admin.business-units.gallery.destroy', [$this->businessUnit->slug, $media->id]));
    
    $response->assertRedirect();
    $response->assertSessionHas('success');
    
    expect($this->businessUnit->fresh()->getMedia('gallery'))->toHaveCount(0);
});

it('can bulk delete gallery images', function () {
    // Upload multiple images
    $file1 = UploadedFile::fake()->image('test1.jpg');
    $file2 = UploadedFile::fake()->image('test2.jpg');
    
    $media1 = $this->businessUnit
        ->addMediaFromRequest('files')
        ->usingFileName($file1->getClientOriginalName())
        ->toMediaCollection('gallery');
        
    $media2 = $this->businessUnit
        ->addMediaFromRequest('files')
        ->usingFileName($file2->getClientOriginalName())
        ->toMediaCollection('gallery');
    
    expect($this->businessUnit->getMedia('gallery'))->toHaveCount(2);
    
    $response = $this->actingAs($this->admin)
        ->post(route('admin.business-units.gallery.bulk-delete', $this->businessUnit->slug), [
            'media_ids' => [$media1->id, $media2->id],
        ]);
    
    $response->assertRedirect();
    $response->assertSessionHas('success');
    
    expect($this->businessUnit->fresh()->getMedia('gallery'))->toHaveCount(0);
});

it('prevents unauthorized access to gallery management', function () {
    $user = User::factory()->create(); // User without permissions
    
    $response = $this->actingAs($user)
        ->get(route('admin.business-units.gallery.index', $this->businessUnit->slug));
    
    $response->assertStatus(403);
});

it('prevents deleting media from other business units', function () {
    $otherBusinessUnit = BusinessUnit::factory()->create();
    
    // Upload image to other business unit
    $file = UploadedFile::fake()->image('test.jpg');
    $media = $otherBusinessUnit
        ->addMediaFromRequest('files')
        ->usingFileName($file->getClientOriginalName())
        ->toMediaCollection('gallery');
    
    // Try to delete from current business unit
    $response = $this->actingAs($this->admin)
        ->delete(route('admin.business-units.gallery.destroy', [$this->businessUnit->slug, $media->id]));
    
    $response->assertStatus(403);
});

it('generates image conversions on upload', function () {
    $file = UploadedFile::fake()->image('test.jpg', 1200, 800);
    
    $this->actingAs($this->admin)
        ->post(route('admin.business-units.gallery.upload', $this->businessUnit->slug), [
            'files' => [$file],
        ]);
    
    $media = $this->businessUnit->getFirstMedia('gallery');
    
    expect($media)->not->toBeNull();
    expect($media->getUrl('thumb'))->toContain('thumb');
    expect($media->getUrl('preview'))->toContain('preview');
});
