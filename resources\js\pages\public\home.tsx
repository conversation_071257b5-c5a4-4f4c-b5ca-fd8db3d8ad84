import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { getBusinessUnitColor, getBusinessUnitIcon, getBusinessUnitImage, otherImages } from '../../lib/images';
import { Head, Link } from '@inertiajs/react';
import { ArrowRight, Building2, Calendar, ChevronDown, ExternalLink, FileText, Globe, Info, Star } from 'lucide-react';

interface BusinessUnit {
    id: number;
    name: string;
    slug: string;
    description: string;
    status: string;
    primary_color?: string;
    contact_info?: {
        phone?: string;
        email?: string;
        address?: string;
    };
    social_media?: {
        instagram?: string;
        facebook?: string;
    };
}

interface NewsArticle {
    id: number;
    title: string;
    slug: string;
    excerpt?: string;
    featured_image?: string;
    published_at: string;
    author: {
        name: string;
    };
}

interface Props {
    business_units: BusinessUnit[];
    recent_news: NewsArticle[];
    settings: Record<string, string>;
}

export default function Home({ business_units, recent_news, settings }: Props) {
    const getStatusBadge = (status: string) => {
        if (status === 'coming_soon') {
            return (
                <span className="inline-flex items-center rounded-full bg-blue-100 px-2 py-1 text-xs font-medium text-blue-800">Segera Hadir</span>
            );
        }
        return null;
    };

    return (
        <>
            <Head title="Cigi Global - Transformasi Digital Masa Depan" />

            <div className="min-h-screen bg-black">
                {/* Header */}
                <header className="border-b border-gray-800 bg-black">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="flex items-center justify-between py-6">
                            <div className="flex items-center">
                                <img src={otherImages.globe} alt="CIGI Global" className="mr-3 h-8 w-8" />
                                <h1 className="text-2xl font-bold text-white">CigiGlobal</h1>
                            </div>
                            <nav className="hidden space-x-8 md:flex">
                                <Link href="/" className="border-b-2 border-orange-500 pb-1 text-white">
                                    Beranda
                                </Link>
                                <div className="group relative">
                                    <button className="flex items-center text-white hover:text-orange-400">
                                        Unit Usaha
                                        <ChevronDown className="ml-1 h-4 w-4" />
                                    </button>
                                </div>
                                <Link href="/about" className="text-white hover:text-orange-400">
                                    Tentang Kami
                                </Link>
                                <Link href="/news" className="text-white hover:text-orange-400">
                                    Berita
                                </Link>
                                <Link href="/contact" className="text-white hover:text-orange-400">
                                    Kontak
                                </Link>
                            </nav>
                        </div>
                    </div>
                </header>

                {/* Hero Section */}
                <section className="py-20">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="grid items-center gap-12 lg:grid-cols-2">
                            {/* Left Column - Text Content */}
                            <div>
                                <div className="mb-6 inline-flex items-center rounded-md bg-gradient-to-r from-orange-500 to-amber-500 px-4 py-2 text-sm font-medium text-white shadow-lg">
                                    <img src={otherImages['cigi-global-2']} alt="CIGI Global Logo" className="mr-2 h-4 w-4 rounded" />
                                    Cimande Girang - Global Solutions, Local Excellence
                                </div>
                                <h1 className="mb-6 text-4xl leading-tight font-bold md:text-6xl">
                                    <span className="bg-gradient-to-r from-orange-400 to-amber-400 bg-clip-text text-transparent">
                                        Transformasi Digital
                                    </span>
                                    <br />
                                    <span className="text-white">Masa Depan</span>
                                </h1>
                                <p className="mb-8 text-lg leading-relaxed text-zinc-300">
                                    PT Cimande Girang Global adalah perusahaan inovatif yang berkomitmen untuk memberikan
                                    <span className="font-semibold text-amber-400"> solusi digital terbaik</span> bagi bisnis Anda. Dengan pengalaman
                                    lebih dari 4 tahun, kami hadir sebagai mitra terpercaya dalam transformasi digital.
                                </p>
                                <div className="flex flex-col gap-4 sm:flex-row">
                                    <Button
                                        size="lg"
                                        className="bg-gradient-to-r from-orange-500 to-amber-500 text-white shadow-lg hover:from-orange-600 hover:to-amber-600"
                                    >
                                        <Info className="mr-2 h-4 w-4" />
                                        Tentang Kami
                                    </Button>
                                    <Button
                                        size="lg"
                                        variant="outline"
                                        className="border-gray-600 text-white hover:border-orange-500 hover:bg-gray-800"
                                    >
                                        <FileText className="mr-2 h-4 w-4" />
                                        Lihat Berita
                                    </Button>
                                </div>
                            </div>

                            {/* Right Column - Business Units Visual */}
                            <div className="relative">
                                <div className="relative">
                                    <div className="absolute -inset-4 animate-pulse rounded-2xl bg-gradient-to-r from-amber-600 to-amber-400 opacity-20 blur-2xl"></div>
                                    <img
                                        src={otherImages['cigi-global-2']}
                                        alt="CIGI Global Office"
                                        className="relative mx-auto w-full max-w-md rounded-2xl shadow-2xl md:max-w-lg"
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                {/* About Section */}
                <section className="bg-gradient-to-br from-gray-900 via-black to-gray-900 py-20">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="mb-16 text-center">
                            <h2 className="mb-6 text-4xl font-bold text-white">Tentang Kami</h2>
                            <p className="mx-auto max-w-4xl text-xl text-zinc-300">
                                Kami adalah perusahaan yang berkomitmen untuk mengembangkan berbagai unit usaha yang berkontribusi pada kemajuan
                                masyarakat desa. Dengan fokus pada inovasi dan keunggulan lokal, kami membangun solusi yang berkelanjutan untuk masa
                                depan.
                            </p>
                        </div>

                        <div className="grid gap-8 md:grid-cols-3">
                            <div className="group text-center transition-transform duration-300 hover:scale-105">
                                <div className="mx-auto mb-6 flex h-20 w-20 items-center justify-center rounded-2xl bg-gradient-to-br from-amber-500 to-amber-600 shadow-lg">
                                    <Building2 className="h-10 w-10 text-white" />
                                </div>
                                <h3 className="mb-4 text-xl font-semibold text-white">Unit Bisnis Terpadu</h3>
                                <p className="text-zinc-300">
                                    Mengembangkan berbagai unit usaha yang saling mendukung dan berkontribusi pada pertumbuhan ekonomi lokal.
                                </p>
                            </div>
                            <div className="group text-center transition-transform duration-300 hover:scale-105">
                                <div className="mx-auto mb-6 flex h-20 w-20 items-center justify-center rounded-2xl bg-gradient-to-br from-amber-500 to-amber-600 shadow-lg">
                                    <Globe className="h-10 w-10 text-white" />
                                </div>
                                <h3 className="mb-4 text-xl font-semibold text-white">Solusi Global</h3>
                                <p className="text-zinc-300">
                                    Memberikan solusi digital yang mengintegrasikan teknologi modern dengan kebutuhan lokal masyarakat.
                                </p>
                            </div>
                            <div className="group text-center transition-transform duration-300 hover:scale-105">
                                <div className="mx-auto mb-6 flex h-20 w-20 items-center justify-center rounded-2xl bg-gradient-to-br from-amber-500 to-amber-600 shadow-lg">
                                    <Star className="h-10 w-10 text-white" />
                                </div>
                                <h3 className="mb-4 text-xl font-semibold text-white">Keunggulan Lokal</h3>
                                <p className="text-zinc-300">Mempertahankan identitas dan nilai-nilai lokal sambil mengadopsi inovasi global.</p>
                            </div>
                        </div>
                    </div>
                </section>

                {/* All Unit Bisnis CIGI Global Section */}
                <section className="bg-black py-20">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="mb-16 text-center">
                            <h2 className="mb-6 text-4xl font-bold text-white">Semua Unit Bisnis CIGI Global</h2>
                            <p className="text-xl text-zinc-400">Berbagai unit usaha yang dikembangkan untuk melayani kebutuhan masyarakat</p>
                        </div>

                        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                            {business_units.map((unit, index) => (
                                <Card
                                    key={unit.id}
                                    className="group cursor-pointer overflow-hidden border-gray-800 bg-gray-900 text-white transition-all hover:scale-105 hover:shadow-xl hover:shadow-orange-500/20"
                                >
                                    {/* Image Header */}
                                    <div className="relative aspect-[4/3] overflow-hidden">
                                        <img
                                            src={getBusinessUnitImage(unit.slug)}
                                            alt={unit.name}
                                            className="h-full w-full object-cover transition-transform duration-300 group-hover:scale-110"
                                        />
                                        {/* Icon Overlay */}
                                        <div
                                            className={`absolute top-3 right-3 rounded-full bg-gradient-to-r ${getBusinessUnitColor(unit.slug)} p-2 shadow-lg`}
                                        >
                                            <span className="text-lg">{getBusinessUnitIcon(unit.slug)}</span>
                                        </div>
                                    </div>

                                    <CardHeader className="pb-3">
                                        <div className="flex items-center justify-between">
                                            <CardTitle className="line-clamp-1 text-lg text-white transition-colors group-hover:text-amber-400">
                                                {unit.name}
                                            </CardTitle>
                                            {getStatusBadge(unit.status)}
                                        </div>
                                    </CardHeader>

                                    <CardContent>
                                        <CardDescription className="mb-4 line-clamp-2 text-zinc-300">{unit.description}</CardDescription>

                                        {unit.contact_info?.phone && (
                                            <div className="mb-3 flex items-center gap-2 text-sm text-zinc-400">
                                                <span>📞</span>
                                                <span>{unit.contact_info.phone}</span>
                                            </div>
                                        )}

                                        {unit.status === 'active' ? (
                                            <Link href={`/${unit.slug}`}>
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    className="w-full border-orange-500 text-orange-500 transition-all duration-300 hover:bg-orange-500 hover:text-white"
                                                >
                                                    <ExternalLink className="mr-2 h-3 w-3" />
                                                    Kunjungi
                                                </Button>
                                            </Link>
                                        ) : (
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                className="w-full cursor-not-allowed border-gray-600 text-zinc-400"
                                                disabled
                                            >
                                                <span className="mr-2">⏳</span>
                                                Segera Hadir
                                            </Button>
                                        )}
                                    </CardContent>
                                </Card>
                            ))}
                        </div>
                    </div>
                </section>

                {/* Galeri Kegiatan Section */}
                <section className="bg-gray-900 py-20">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="mb-16 text-center">
                            <h2 className="mb-6 text-4xl font-bold text-white">Galeri Kegiatan</h2>
                            <p className="text-xl text-zinc-400">Dokumentasi berbagai aktivitas dan kegiatan yang telah kami lakukan</p>
                        </div>

                        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
                            <div className="group overflow-hidden rounded-lg bg-gray-800 transition-all duration-500 hover:scale-105">
                                <div className="aspect-video overflow-hidden">
                                    <img
                                        src={otherImages['cigi-global']}
                                        alt="CIGI Global Activities"
                                        className="h-full w-full object-cover transition-transform duration-500 group-hover:scale-110"
                                    />
                                </div>
                                <div className="p-4">
                                    <h3 className="mb-2 text-lg font-semibold text-white">Kegiatan CIGI Global</h3>
                                    <p className="text-sm text-zinc-300">
                                        Kegiatan dan aktivitas utama perusahaan CIGI Global dalam mengembangkan unit bisnis.
                                    </p>
                                </div>
                            </div>
                            <div className="group overflow-hidden rounded-lg bg-gray-800 transition-all duration-500 hover:scale-105">
                                <div className="aspect-video overflow-hidden">
                                    <img
                                        src={otherImages['cigi-global-2']}
                                        alt="CIGI Global Activities 2"
                                        className="h-full w-full object-cover transition-transform duration-500 group-hover:scale-110"
                                    />
                                </div>
                                <div className="p-4">
                                    <h3 className="mb-2 text-lg font-semibold text-white">Kegiatan CIGI Global 2</h3>
                                    <p className="text-sm text-zinc-300">Kegiatan lanjutan dalam pengembangan dan pengelolaan unit bisnis.</p>
                                </div>
                            </div>
                            <div className="group overflow-hidden rounded-lg bg-gray-800 transition-all duration-500 hover:scale-105">
                                <div className="aspect-video overflow-hidden">
                                    <img
                                        src={otherImages['hero-img']}
                                        alt="Hero Activities"
                                        className="h-full w-full object-cover transition-transform duration-500 group-hover:scale-110"
                                    />
                                </div>
                                <div className="p-4">
                                    <h3 className="mb-2 text-lg font-semibold text-white">Kegiatan Hero</h3>
                                    <p className="text-sm text-zinc-300">Kegiatan hero dan aktivitas utama dalam transformasi digital.</p>
                                </div>
                            </div>
                            <div className="group overflow-hidden rounded-lg bg-gray-800 transition-all duration-500 hover:scale-105">
                                <div className="aspect-video overflow-hidden">
                                    <img
                                        src="/assets/unit-usaha/cigi-farm-2.jpg"
                                        alt="CIGI Farm Activities"
                                        className="h-full w-full object-cover transition-transform duration-500 group-hover:scale-110"
                                    />
                                </div>
                                <div className="p-4">
                                    <h3 className="mb-2 text-lg font-semibold text-white">Kegiatan CIGI Farm</h3>
                                    <p className="text-sm text-zinc-300">Aktivitas dan kegiatan dalam pengembangan unit bisnis pertanian.</p>
                                </div>
                            </div>
                            <div className="group overflow-hidden rounded-lg bg-gray-800 transition-all duration-500 hover:scale-105">
                                <div className="aspect-video overflow-hidden">
                                    <img
                                        src="/assets/unit-usaha/cigi-farm-3.jpg"
                                        alt="CIGI Farm Activities 3"
                                        className="h-full w-full object-cover transition-transform duration-500 group-hover:scale-110"
                                    />
                                </div>
                                <div className="p-4">
                                    <h3 className="mb-2 text-lg font-semibold text-white">Kegiatan CIGI Farm 3</h3>
                                    <p className="text-sm text-zinc-300">Kegiatan lanjutan dalam pengembangan pertanian dan agribisnis.</p>
                                </div>
                            </div>
                            <div className="group overflow-hidden rounded-lg bg-gray-800 transition-all duration-500 hover:scale-105">
                                <div className="aspect-video overflow-hidden">
                                    <img
                                        src="/assets/unit-usaha/cigi-mart.jpg"
                                        alt="CIGI Mart Activities"
                                        className="h-full w-full object-cover transition-transform duration-500 group-hover:scale-110"
                                    />
                                </div>
                                <div className="p-4">
                                    <h3 className="mb-2 text-lg font-semibold text-white">Kegiatan CIGI Mart</h3>
                                    <p className="text-sm text-zinc-300">Aktivitas dalam pengembangan unit bisnis retail dan perdagangan.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                {/* Berita & Update Terbaru Section */}
                {recent_news.length > 0 && (
                    <section className="bg-black py-20">
                        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                            <div className="mb-16 flex items-center justify-between">
                                <div>
                                    <h2 className="mb-6 text-4xl font-bold text-white">Berita & Update Terbaru</h2>
                                    <p className="text-xl text-zinc-400">Aktivitas dan perkembangan terbaru dari CIGI Global</p>
                                </div>
                                <Link href="/news">
                                    <Button variant="outline" className="border-orange-500 text-orange-500 hover:bg-orange-500 hover:text-white">
                                        Lihat Semua Berita
                                        <ArrowRight className="ml-2 h-4 w-4" />
                                    </Button>
                                </Link>
                            </div>

                            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                                {recent_news.slice(0, 6).map((article, index) => (
                                    <Card
                                        key={article.id}
                                        className="group cursor-pointer border-gray-800 bg-gray-900 text-white transition-all hover:scale-105 hover:shadow-xl hover:shadow-orange-500/20"
                                    >
                                        {article.featured_image && (
                                            <div className="aspect-video overflow-hidden">
                                                <img
                                                    src={article.featured_image}
                                                    alt={article.title}
                                                    className="h-full w-full object-cover transition-transform duration-500 group-hover:scale-110"
                                                />
                                            </div>
                                        )}
                                        <CardHeader>
                                            <CardTitle className="line-clamp-2 text-lg text-white transition-colors group-hover:text-amber-400">
                                                {article.title}
                                            </CardTitle>
                                            <div className="flex items-center gap-2 text-sm text-zinc-400">
                                                <Calendar className="h-3 w-3" />
                                                <span>{new Date(article.published_at).toLocaleDateString('id-ID')}</span>
                                                <span>•</span>
                                                <span>{article.author.name}</span>
                                            </div>
                                        </CardHeader>
                                        <CardContent>
                                            {article.excerpt && (
                                                <CardDescription className="mb-4 line-clamp-3 text-zinc-300">{article.excerpt}</CardDescription>
                                            )}
                                            <Link href={`/news/${article.slug}`}>
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    className="border-orange-500 text-orange-500 hover:bg-orange-500 hover:text-white"
                                                >
                                                    Baca Selengkapnya
                                                </Button>
                                            </Link>
                                        </CardContent>
                                    </Card>
                                ))}
                            </div>
                        </div>
                    </section>
                )}

                {/* Hubungi Kami Section */}
                <section className="bg-gradient-to-br from-gray-900 via-black to-gray-900 py-20">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="mb-16 text-center">
                            <h2 className="mb-6 text-4xl font-bold text-white">Hubungi Kami</h2>
                            <p className="text-xl text-zinc-400">Kami siap membantu dan menjawab pertanyaan Anda</p>
                        </div>

                        <div className="grid gap-12 lg:grid-cols-2">
                            {/* Contact Form */}
                            <div>
                                <form className="space-y-6">
                                    <div className="grid gap-6 md:grid-cols-2">
                                        <div>
                                            <label htmlFor="firstName" className="block text-sm font-medium text-white">
                                                Nama Depan
                                            </label>
                                            <input
                                                type="text"
                                                id="firstName"
                                                className="mt-2 w-full rounded-lg border border-gray-600 bg-gray-800 px-4 py-3 text-white placeholder-gray-400 focus:border-orange-500 focus:outline-none focus:ring-2 focus:ring-orange-500/20"
                                                placeholder="Masukkan nama depan"
                                            />
                                        </div>
                                        <div>
                                            <label htmlFor="lastName" className="block text-sm font-medium text-white">
                                                Nama Belakang
                                            </label>
                                            <input
                                                type="text"
                                                id="lastName"
                                                className="mt-2 w-full rounded-lg border border-gray-600 bg-gray-800 px-4 py-3 text-white placeholder-gray-400 focus:border-orange-500 focus:outline-none focus:ring-2 focus:ring-orange-500/20"
                                                placeholder="Masukkan nama belakang"
                                            />
                                        </div>
                                    </div>
                                    <div>
                                        <label htmlFor="email" className="block text-sm font-medium text-white">
                                            Email
                                        </label>
                                        <input
                                            type="email"
                                            id="email"
                                            className="mt-2 w-full rounded-lg border border-gray-600 bg-gray-800 px-4 py-3 text-white placeholder-gray-400 focus:border-orange-500 focus:outline-none focus:ring-2 focus:ring-orange-500/20"
                                            placeholder="Masukkan email"
                                        />
                                    </div>
                                    <div>
                                        <label htmlFor="message" className="block text-sm font-medium text-white">
                                            Pesan
                                        </label>
                                        <textarea
                                            id="message"
                                            rows={4}
                                            className="mt-2 w-full rounded-lg border border-gray-600 bg-gray-800 px-4 py-3 text-white placeholder-gray-400 focus:border-orange-500 focus:outline-none focus:ring-2 focus:ring-orange-500/20"
                                            placeholder="Tulis pesan Anda"
                                        ></textarea>
                                    </div>
                                    <Button
                                        type="submit"
                                        size="lg"
                                        className="w-full bg-gradient-to-r from-orange-500 to-amber-500 text-white shadow-lg hover:from-orange-600 hover:to-amber-600"
                                    >
                                        Kirim Pesan
                                    </Button>
                                </form>
                            </div>

                            {/* Contact Information */}
                            <div className="space-y-8">
                                <div>
                                    <h3 className="mb-4 text-2xl font-semibold text-white">Informasi Kontak</h3>
                                    <div className="space-y-4">
                                        <div className="flex items-start gap-3">
                                            <div className="mt-1 rounded-full bg-gradient-to-r from-orange-500 to-amber-500 p-2">
                                                <span className="text-lg">📍</span>
                                            </div>
                                            <div>
                                                <h4 className="font-medium text-white">Alamat</h4>
                                                <p className="text-zinc-300">Jl. Cimande Girang No. 123, Bogor, Jawa Barat</p>
                                            </div>
                                        </div>
                                        <div className="flex items-start gap-3">
                                            <div className="mt-1 rounded-full bg-gradient-to-r from-orange-500 to-amber-500 p-2">
                                                <span className="text-lg">📞</span>
                                            </div>
                                            <div>
                                                <h4 className="font-medium text-white">Telepon</h4>
                                                <p className="text-zinc-300">+62 21 1234 5678</p>
                                            </div>
                                        </div>
                                        <div className="flex items-start gap-3">
                                            <div className="mt-1 rounded-full bg-gradient-to-r from-orange-500 to-amber-500 p-2">
                                                <span className="text-lg">✉️</span>
                                            </div>
                                            <div>
                                                <h4 className="font-medium text-white">Email</h4>
                                                <p className="text-zinc-300"><EMAIL></p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div>
                                    <h3 className="mb-4 text-2xl font-semibold text-white">Jam Operasional</h3>
                                    <div className="space-y-2 text-zinc-300">
                                        <p>Senin - Jumat: 08:00 - 17:00</p>
                                        <p>Sabtu: 08:00 - 12:00</p>
                                        <p>Minggu: Tutup</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                {/* Footer */}
                <footer className="border-t border-gray-800 bg-black py-12">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="grid gap-8 md:grid-cols-4">
                            <div className="md:col-span-2">
                                <div className="flex items-center">
                                    <img src={otherImages.globe} alt="CIGI Global" className="mr-3 h-8 w-8" />
                                    <h3 className="text-xl font-bold text-white">CigiGlobal</h3>
                                </div>
                                <p className="mt-4 text-zinc-400">
                                    Transformasi Digital Masa Depan. Kami berkomitmen untuk memberikan solusi digital terbaik bagi bisnis Anda.
                                </p>
                            </div>
                            <div>
                                <h4 className="mb-4 text-lg font-semibold text-white">Unit Usaha</h4>
                                <ul className="space-y-2 text-zinc-400">
                                    <li>
                                        <Link href="/cigi-net" className="hover:text-orange-400">
                                            CIGI Net
                                        </Link>
                                    </li>
                                    <li>
                                        <Link href="/cigi-farm" className="hover:text-orange-400">
                                            CIGI Farm
                                        </Link>
                                    </li>
                                    <li>
                                        <Link href="/cigi-food" className="hover:text-orange-400">
                                            CIGI Food
                                        </Link>
                                    </li>
                                </ul>
                            </div>
                            <div>
                                <h4 className="mb-4 text-lg font-semibold text-white">Tautan</h4>
                                <ul className="space-y-2 text-zinc-400">
                                    <li>
                                        <Link href="/about" className="hover:text-orange-400">
                                            Tentang Kami
                                        </Link>
                                    </li>
                                    <li>
                                        <Link href="/news" className="hover:text-orange-400">
                                            Berita
                                        </Link>
                                    </li>
                                    <li>
                                        <Link href="/contact" className="hover:text-orange-400">
                                            Kontak
                                        </Link>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div className="mt-8 border-t border-gray-800 pt-8 text-center text-zinc-400">
                            <p>&copy; 2024 CIGI Global. Semua hak dilindungi.</p>
                        </div>
                    </div>
                </footer>
            </div>
        </>
    );
}
