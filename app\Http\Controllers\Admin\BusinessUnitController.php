<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\BusinessUnit;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Inertia\Inertia;
use Inertia\Response;

class BusinessUnitController extends Controller
{
    use AuthorizesRequests;

    public function index(): Response
    {
        $this->authorize('view business units');

        $business_units = BusinessUnit::with(['pages', 'galleries'])
            ->ordered()
            ->get()
            ->map(function ($unit) {
                return [
                    'id' => $unit->id,
                    'name' => $unit->name,
                    'slug' => $unit->slug,
                    'description' => $unit->description,
                    'status' => $unit->status,
                    'order' => $unit->order,
                    'pages_count' => $unit->pages->count(),
                    'galleries_count' => $unit->galleries->count(),
                    'contact_info' => $unit->contact_info,
                    'social_media' => $unit->social_media,
                    'primary_color' => $unit->primary_color,
                    'created_at' => $unit->created_at,
                ];
            });

        return Inertia::render('admin/business-units/index', [
            'business_units' => $business_units,
        ]);
    }

    public function create(): Response
    {
        $this->authorize('create business units');

        return Inertia::render('admin/business-units/create');
    }

    public function store(Request $request): RedirectResponse
    {
        $this->authorize('create business units');

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:business_units,slug',
            'description' => 'nullable|string',
            'status' => 'required|in:active,inactive,coming_soon',
            'order' => 'nullable|integer|min:0',
            'contact_info' => 'nullable|array',
            'contact_info.phone' => 'nullable|string',
            'contact_info.email' => 'nullable|email',
            'contact_info.address' => 'nullable|string',
            'social_media' => 'nullable|array',
            'social_media.instagram' => 'nullable|string',
            'social_media.facebook' => 'nullable|string',
            'primary_color' => 'nullable|string|max:7',
        ]);

        if (empty($validated['slug'])) {
            $validated['slug'] = Str::slug($validated['name']);
        }

        if (empty($validated['order'])) {
            $validated['order'] = BusinessUnit::max('order') + 1;
        }

        BusinessUnit::create($validated);

        return redirect()
            ->route('admin.business-units.index')
            ->with('success', 'Unit bisnis berhasil dibuat.');
    }

    public function show(BusinessUnit $business_unit): Response
    {
        $this->authorize('view business units');

    $business_unit->load(['pages', 'galleries']);

        return Inertia::render('admin/business-units/show', [
            'business_unit' => $business_unit,
        ]);
    }

    public function edit(BusinessUnit $business_unit): Response
    {
        $this->authorize('edit business units');

        return Inertia::render('admin/business-units/edit', [
            'business_unit' => $business_unit,
        ]);
    }

    public function update(Request $request, BusinessUnit $business_unit): RedirectResponse
    {
        $this->authorize('edit business units');

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'slug' => 'required|string|max:255|unique:business_units,slug,'.$business_unit->id,
            'description' => 'nullable|string',
            'status' => 'required|in:active,inactive,coming_soon',
            'order' => 'nullable|integer|min:0',
            'contact_info' => 'nullable|array',
            'contact_info.phone' => 'nullable|string',
            'contact_info.email' => 'nullable|email',
            'contact_info.address' => 'nullable|string',
            'social_media' => 'nullable|array',
            'social_media.instagram' => 'nullable|string',
            'social_media.facebook' => 'nullable|string',
            'primary_color' => 'nullable|string|max:7',
        ]);

    $business_unit->update($validated);

        return redirect()
            ->route('admin.business-units.index')
            ->with('success', 'Unit bisnis berhasil diperbarui.');
    }

    public function destroy(BusinessUnit $business_unit): RedirectResponse
    {
        $this->authorize('delete business units');

    $business_unit->delete();

        return redirect()
            ->route('admin.business-units.index')
            ->with('success', 'Unit bisnis berhasil dihapus.');
    }
}
