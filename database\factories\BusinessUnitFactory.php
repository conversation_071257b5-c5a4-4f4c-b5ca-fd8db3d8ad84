<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\BusinessUnit>
 */
class BusinessUnitFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $name = fake()->company();

        return [
            'name' => $name,
            'slug' => Str::slug($name),
            'description' => fake()->paragraph(),
            'status' => fake()->randomElement(['active', 'inactive', 'coming_soon']),
            'order' => fake()->numberBetween(1, 10),
            'contact_info' => [
                'phone' => fake()->phoneNumber(),
                'email' => fake()->companyEmail(),
                'address' => fake()->address(),
            ],
            'social_media' => [
                'instagram' => '@'.Str::slug($name),
                'facebook' => $name,
            ],
            'primary_color' => fake()->hexColor(),
        ];
    }

    /**
     * Indicate that the business unit is active.
     */
    public function active(): static
    {
        return $this->state(fn () => [
            'status' => 'active',
        ]);
    }

    /**
     * Indicate that the business unit is coming soon.
     */
    public function comingSoon(): static
    {
        return $this->state(fn () => [
            'status' => 'coming_soon',
        ]);
    }
}
