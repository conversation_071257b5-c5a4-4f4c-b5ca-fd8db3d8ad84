import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, useForm } from '@inertiajs/react';
import { ArrowLeft, Save } from 'lucide-react';

interface FormData {
    title: string;
    slug: string;
    excerpt: string;
    content: string;
    featured_image: string;
    status: string;
    published_at: string;
    seo_data: {
        title: string;
        description: string;
        keywords: string;
    };
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Admin',
        href: '/admin',
    },
    {
        title: 'Berita',
        href: '/admin/news',
    },
    {
        title: 'Tambah Artikel',
        href: '/admin/news/create',
    },
];

export default function CreateNews() {
    const { data, setData, post, processing, errors } = useForm<FormData>({
        title: '',
        slug: '',
        excerpt: '',
        content: '',
        featured_image: '',
        status: 'draft',
        published_at: '',
        seo_data: {
            title: '',
            description: '',
            keywords: '',
        },
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post(route('admin.news.store'));
    };

    const generateSlug = (title: string) => {
        const slug = title
            .toLowerCase()
            .replace(/[^a-z0-9\s-]/g, '')
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-')
            .trim();
        setData('slug', slug);
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Tambah Artikel Berita" />

            <div className="flex h-full flex-1 flex-col gap-4 p-4">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold tracking-tight">Tambah Artikel Berita</h1>
                        <p className="text-muted-foreground">Buat artikel berita baru untuk Cigi Global</p>
                    </div>
                    <Link href="/admin/news">
                        <Button variant="outline">
                            <ArrowLeft className="mr-2 h-4 w-4" />
                            Kembali
                        </Button>
                    </Link>
                </div>

                {/* Form */}
                <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid gap-6 md:grid-cols-3">
                        {/* Main Content */}
                        <div className="space-y-6 md:col-span-2">
                            <Card>
                                <CardHeader>
                                    <CardTitle>Konten Artikel</CardTitle>
                                    <CardDescription>Informasi utama artikel berita</CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="title">Judul Artikel</Label>
                                        <Input
                                            id="title"
                                            value={data.title}
                                            onChange={(e) => {
                                                setData('title', e.target.value);
                                                if (!data.slug) {
                                                    generateSlug(e.target.value);
                                                }
                                                if (!data.seo_data.title) {
                                                    setData('seo_data', { ...data.seo_data, title: e.target.value });
                                                }
                                            }}
                                            placeholder="Masukkan judul artikel"
                                            className={errors.title ? 'border-red-500' : ''}
                                        />
                                        {errors.title && <p className="text-sm text-red-500">{errors.title}</p>}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="slug">URL Slug</Label>
                                        <Input
                                            id="slug"
                                            value={data.slug}
                                            onChange={(e) => setData('slug', e.target.value)}
                                            placeholder="url-artikel"
                                            className={errors.slug ? 'border-red-500' : ''}
                                        />
                                        {errors.slug && <p className="text-sm text-red-500">{errors.slug}</p>}
                                        <p className="text-xs text-muted-foreground">URL akan menjadi: /news/{data.slug}</p>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="excerpt">Ringkasan</Label>
                                        <Textarea
                                            id="excerpt"
                                            value={data.excerpt}
                                            onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => {
                                                setData('excerpt', e.target.value);
                                                if (!data.seo_data.description) {
                                                    setData('seo_data', { ...data.seo_data, description: e.target.value });
                                                }
                                            }}
                                            placeholder="Ringkasan singkat artikel (opsional)"
                                            rows={3}
                                            className={errors.excerpt ? 'border-red-500' : ''}
                                        />
                                        {errors.excerpt && <p className="text-sm text-red-500">{errors.excerpt}</p>}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="content">Konten Artikel</Label>
                                        <Textarea
                                            id="content"
                                            value={data.content}
                                            onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setData('content', e.target.value)}
                                            placeholder="Tulis konten artikel di sini..."
                                            rows={15}
                                            className={errors.content ? 'border-red-500' : ''}
                                        />
                                        {errors.content && <p className="text-sm text-red-500">{errors.content}</p>}
                                    </div>
                                </CardContent>
                            </Card>

                            {/* SEO Settings */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Pengaturan SEO</CardTitle>
                                    <CardDescription>Optimasi mesin pencari</CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="seo_title">Judul SEO</Label>
                                        <Input
                                            id="seo_title"
                                            value={data.seo_data.title}
                                            onChange={(e) => setData('seo_data', { ...data.seo_data, title: e.target.value })}
                                            placeholder="Judul untuk mesin pencari"
                                        />
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="seo_description">Deskripsi SEO</Label>
                                        <Textarea
                                            id="seo_description"
                                            value={data.seo_data.description}
                                            onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
                                                setData('seo_data', { ...data.seo_data, description: e.target.value })
                                            }
                                            placeholder="Deskripsi untuk mesin pencari"
                                            rows={3}
                                        />
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="seo_keywords">Kata Kunci</Label>
                                        <Input
                                            id="seo_keywords"
                                            value={data.seo_data.keywords}
                                            onChange={(e) => setData('seo_data', { ...data.seo_data, keywords: e.target.value })}
                                            placeholder="kata kunci, dipisahkan, dengan, koma"
                                        />
                                    </div>
                                </CardContent>
                            </Card>
                        </div>

                        {/* Sidebar */}
                        <div className="space-y-6">
                            <Card>
                                <CardHeader>
                                    <CardTitle>Pengaturan Publikasi</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="status">Status</Label>
                                        <Select value={data.status} onValueChange={(value) => setData('status', value)}>
                                            <SelectTrigger>
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="draft">Draft</SelectItem>
                                                <SelectItem value="published">Dipublikasi</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    {data.status === 'published' && (
                                        <div className="space-y-2">
                                            <Label htmlFor="published_at">Tanggal Publikasi</Label>
                                            <Input
                                                id="published_at"
                                                type="datetime-local"
                                                value={data.published_at}
                                                onChange={(e) => setData('published_at', e.target.value)}
                                            />
                                        </div>
                                    )}
                                </CardContent>
                            </Card>

                            <Card>
                                <CardHeader>
                                    <CardTitle>Gambar Unggulan</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="featured_image">URL Gambar</Label>
                                        <Input
                                            id="featured_image"
                                            value={data.featured_image}
                                            onChange={(e) => setData('featured_image', e.target.value)}
                                            placeholder="https://example.com/image.jpg"
                                        />
                                    </div>
                                    {data.featured_image && (
                                        <div className="aspect-video overflow-hidden rounded-lg border">
                                            <img
                                                src={data.featured_image}
                                                alt="Preview"
                                                className="h-full w-full object-cover"
                                                onError={(e) => {
                                                    e.currentTarget.style.display = 'none';
                                                }}
                                            />
                                        </div>
                                    )}
                                </CardContent>
                            </Card>
                        </div>
                    </div>

                    {/* Submit Button */}
                    <div className="flex justify-end">
                        <Button type="submit" disabled={processing}>
                            <Save className="mr-2 h-4 w-4" />
                            {processing ? 'Menyimpan...' : 'Simpan Artikel'}
                        </Button>
                    </div>
                </form>
            </div>
        </AppLayout>
    );
}
