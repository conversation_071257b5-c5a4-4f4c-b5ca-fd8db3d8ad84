<?php

namespace Database\Seeders;

use App\Models\GlobalSetting;
use Illuminate\Database\Seeder;

class GlobalSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            // Site Information
            [
                'key' => 'site_title',
                'value' => 'Cigi Global',
                'type' => 'text',
                'group' => 'site',
                'description' => 'Judul website yang akan muncul di tab browser',
            ],
            [
                'key' => 'site_description',
                'value' => 'Pusat bisnis terpadu yang mengembangkan berbagai unit usaha untuk kemajuan masyarakat desa',
                'type' => 'textarea',
                'group' => 'site',
                'description' => 'Deskripsi website untuk SEO',
            ],
            [
                'key' => 'site_keywords',
                'value' => 'cigi global, bisnis, desa, unit usaha, badminton, internet, retail, kuliner, sepak bola, pertanian, panahan',
                'type' => 'text',
                'group' => 'site',
                'description' => 'Kata kunci untuk SEO, pisahkan dengan koma',
            ],

            // Homepage Hero
            [
                'key' => 'hero_title',
                'value' => 'Selamat Datang di Cigi Global',
                'type' => 'text',
                'group' => 'homepage',
                'description' => 'Judul utama di halaman beranda',
            ],
            [
                'key' => 'hero_subtitle',
                'value' => 'Pusat Bisnis Terpadu',
                'type' => 'text',
                'group' => 'homepage',
                'description' => 'Subjudul di halaman beranda',
            ],
            [
                'key' => 'hero_description',
                'value' => 'Mengembangkan berbagai unit usaha untuk kemajuan masyarakat desa dengan inovasi dan dedikasi tinggi',
                'type' => 'textarea',
                'group' => 'homepage',
                'description' => 'Deskripsi di bagian hero halaman beranda',
            ],
            [
                'key' => 'hero_image',
                'value' => '',
                'type' => 'image',
                'group' => 'homepage',
                'description' => 'URL gambar latar belakang hero',
            ],

            // About Section
            [
                'key' => 'about_title',
                'value' => 'Tentang Cigi Global',
                'type' => 'text',
                'group' => 'homepage',
                'description' => 'Judul bagian tentang di beranda',
            ],
            [
                'key' => 'about_description',
                'value' => 'Cigi Global adalah pusat bisnis terpadu yang berkomitmen mengembangkan berbagai unit usaha untuk kemajuan dan kesejahteraan masyarakat desa.',
                'type' => 'textarea',
                'group' => 'homepage',
                'description' => 'Deskripsi tentang Cigi Global',
            ],

            // Contact Information
            [
                'key' => 'contact_phone',
                'value' => '+62 812-3456-7890',
                'type' => 'text',
                'group' => 'contact',
                'description' => 'Nomor textepon utama',
            ],
            [
                'key' => 'contact_text',
                'value' => '<EMAIL>',
                'type' => 'text',
                'group' => 'contact',
                'description' => 'Email kontak utama',
            ],
            [
                'key' => 'contact_address',
                'value' => 'Jl. Contoh No. 123, Desa Cigi, Kecamatan Contoh, Kabupaten Contoh',
                'type' => 'textarea',
                'group' => 'contact',
                'description' => 'Alamat lengkap',
            ],
            [
                'key' => 'contact_whatsapp',
                'value' => '+62 812-3456-7890',
                'type' => 'text',
                'group' => 'contact',
                'description' => 'Nomor WhatsApp',
            ],

            // Social Media
            [
                'key' => 'social_instagram',
                'value' => 'cigiglobal',
                'type' => 'text',
                'group' => 'social',
                'description' => 'Username Instagram (tanpa @)',
            ],
            [
                'key' => 'social_facebook',
                'value' => 'Cigi Global',
                'type' => 'text',
                'group' => 'social',
                'description' => 'Nama halaman Facebook',
            ],
            [
                'key' => 'social_youtube',
                'value' => 'CigiGlobal',
                'type' => 'text',
                'group' => 'social',
                'description' => 'Channel YouTube',
            ],
            [
                'key' => 'social_tiktok',
                'value' => 'cigiglobal',
                'type' => 'text',
                'group' => 'social',
                'description' => 'Username TikTok (tanpa @)',
            ],

            // SEO Settings
            [
                'key' => 'google_analytics_id',
                'value' => '',
                'type' => 'text',
                'group' => 'seo',
                'description' => 'Google Analytics Tracking ID',
            ],
            [
                'key' => 'google_tag_manager_id',
                'value' => '',
                'type' => 'text',
                'group' => 'seo',
                'description' => 'Google Tag Manager ID',
            ],
            [
                'key' => 'facebook_pixel_id',
                'value' => '',
                'type' => 'text',
                'group' => 'seo',
                'description' => 'Facebook Pixel ID',
            ],

            // Email Settings
            [
                'key' => 'admin_text',
                'value' => '<EMAIL>',
                'type' => 'text',
                'group' => 'text',
                'description' => 'Email administrator',
            ],
            [
                'key' => 'contact_form_text',
                'value' => '<EMAIL>',
                'type' => 'text',
                'group' => 'text',
                'description' => 'Email penerima form kontak',
            ],

            // Maintenance
            [
                'key' => 'maintenance_mode',
                'value' => 'false',
                'type' => 'boolean',
                'group' => 'maintenance',
                'description' => 'Mode pemeliharaan website',
            ],
            [
                'key' => 'maintenance_message',
                'value' => 'Website sedang dalam pemeliharaan. Silakan kembali lagi nanti.',
                'type' => 'textarea',
                'group' => 'maintenance',
                'description' => 'Pesan yang ditampilkan saat maintenance',
            ],
        ];

        foreach ($settings as $setting) {
            GlobalSetting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}
