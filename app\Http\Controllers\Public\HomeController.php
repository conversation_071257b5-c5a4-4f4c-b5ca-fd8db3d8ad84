<?php

namespace App\Http\Controllers\Public;

use App\Http\Controllers\Controller;
use App\Http\Requests\ContactFormRequest;
use App\Mail\ContactFormSubmitted;
use App\Models\BusinessUnit;
use App\Models\ContactSubmission;
use App\Models\GlobalSetting;
use App\Models\NewsArticle;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Mail;
use Inertia\Inertia;
use Inertia\Response;

class HomeController extends Controller
{
    public function index(): Response
    {
        // Get all active business units ordered by their order field
        $businessUnits = BusinessUnit::active()
            ->ordered()
            ->get()
            ->map(function ($unit) {
                return [
                    'id' => $unit->id,
                    'name' => $unit->name,
                    'slug' => $unit->slug,
                    'description' => $unit->description,
                    'status' => $unit->status,
                    'primary_color' => $unit->primary_color,
                    'contact_info' => $unit->contact_info,
                    'social_media' => $unit->social_media,
                ];
            });

        // Get recent news articles
        $recentNews = NewsArticle::where('status', 'published')
            ->with('author')
            ->latest('published_at')
            ->limit(6)
            ->get()
            ->map(function ($article) {
                return [
                    'id' => $article->id,
                    'title' => $article->title,
                    'slug' => $article->slug,
                    'excerpt' => $article->excerpt,
                    'featured_image' => $article->featured_image,
                    'published_at' => $article->published_at,
                    'author' => [
                        'name' => $article->author->name,
                    ],
                ];
            });

        // Get global settings for the homepage
        $globalSettings = GlobalSetting::whereIn('key', [
            'site_title',
            'site_description',
            'hero_title',
            'hero_subtitle',
            'hero_description',
            'hero_image',
            'about_title',
            'about_description',
            'contact_phone',
            'contact_email',
            'contact_address',
            'social_instagram',
            'social_facebook',
            'social_youtube',
        ])->pluck('value', 'key');

        return Inertia::render('public/home', [
            'business_units' => $businessUnits,
            'recent_news' => $recentNews,
            'settings' => $globalSettings,
        ]);
    }

    public function contact(): Response
    {
        // Get global settings for contact information
        $globalSettings = GlobalSetting::whereIn('key', [
            'site_title',
            'site_description',
            'contact_phone',
            'contact_email',
            'contact_address',
            'social_instagram',
            'social_facebook',
            'social_youtube',
        ])->pluck('value', 'key');

        return Inertia::render('public/contact', [
            'settings' => $globalSettings,
        ]);
    }

    public function submitContact(ContactFormRequest $request): RedirectResponse
    {
        $validated = $request->validated();

        // Store the global contact submission (business_unit_id = null)
        $submission = ContactSubmission::create([
            'business_unit_id' => null, // Global contact
            'name' => $validated['name'],
            'email' => $validated['email'],
            'phone' => $validated['phone'] ?? null,
            'subject' => $validated['subject'],
            'message' => $validated['message'],
            'metadata' => [
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'submitted_at' => now()->toISOString(),
                'type' => 'global', // Mark as global contact
            ],
        ]);

        // Send email notification
        try {
            // Use global contact email or fallback to app default
            $globalSettings = GlobalSetting::whereIn('key', ['contact_email'])->pluck('value', 'key');
            $recipientEmail = $globalSettings['contact_email'] ?? config('mail.from.address');

            Mail::to($recipientEmail)->send(new ContactFormSubmitted($submission));
        } catch (\Exception $e) {
            // Log the error but don't fail the submission
            \Log::error('Failed to send global contact form email', [
                'submission_id' => $submission->id,
                'error' => $e->getMessage(),
            ]);
        }

        return back()->with('success', 'Pesan Anda telah terkirim. Tim Cigi Global akan segera menghubungi Anda.');
    }
}
