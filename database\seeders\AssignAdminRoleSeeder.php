<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;

class AssignAdminRoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the first user and assign super-admin role
        $user = User::first();

        if ($user) {
            // Remove any existing roles first
            $user->syncRoles([]);

            // Assign super-admin role
            $user->assignRole('super-admin');

            echo "Assigned super-admin role to user: {$user->email}\n";
        } else {
            // Create a default admin user if no users exist
            $user = User::create([
                'name' => 'Super Admin',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
            ]);

            $user->assignRole('super-admin');

            echo "Created super-admin user: {$user->email} with password: password\n";
        }
    }
}
