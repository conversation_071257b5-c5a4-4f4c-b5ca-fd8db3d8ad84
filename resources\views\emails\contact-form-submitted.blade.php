<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pesan Kontak Baru - {{ $businessUnit->name }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: {{ $businessUnit ? $businessUnit->primary_color ?? '#4285F4' : '#4285F4' }};
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }
        .content {
            background-color: #f9f9f9;
            padding: 30px;
            border-radius: 0 0 8px 8px;
        }
        .field {
            margin-bottom: 20px;
        }
        .field-label {
            font-weight: bold;
            color: #555;
            margin-bottom: 5px;
        }
        .field-value {
            background-color: white;
            padding: 10px;
            border-radius: 4px;
            border-left: 4px solid {{ $businessUnit ? $businessUnit->primary_color ?? '#4285F4' : '#4285F4' }};
        }
        .message-content {
            background-color: white;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid {{ $businessUnit ? $businessUnit->primary_color ?? '#4285F4' : '#4285F4' }};
            white-space: pre-wrap;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            font-size: 12px;
            color: #666;
        }
        .metadata {
            background-color: #f0f0f0;
            padding: 15px;
            border-radius: 4px;
            margin-top: 20px;
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Pesan Kontak Baru</h1>
        <p>{{ $businessUnit ? $businessUnit->name : 'Cigi Global' }}</p>
    </div>

    <div class="content">
        <p>Anda menerima pesan kontak baru melalui website {{ $businessUnit ? $businessUnit->name : 'Cigi Global' }}.</p>

        <div class="field">
            <div class="field-label">Nama:</div>
            <div class="field-value">{{ $submission->name }}</div>
        </div>

        <div class="field">
            <div class="field-label">Email:</div>
            <div class="field-value">
                <a href="mailto:{{ $submission->email }}">{{ $submission->email }}</a>
            </div>
        </div>

        @if($submission->phone)
        <div class="field">
            <div class="field-label">Telepon:</div>
            <div class="field-value">
                <a href="tel:{{ $submission->phone }}">{{ $submission->phone }}</a>
            </div>
        </div>
        @endif

        <div class="field">
            <div class="field-label">Subjek:</div>
            <div class="field-value">{{ $submission->subject }}</div>
        </div>

        <div class="field">
            <div class="field-label">Pesan:</div>
            <div class="message-content">{{ $submission->message }}</div>
        </div>

        <div class="metadata">
            <strong>Informasi Tambahan:</strong><br>
            Dikirim pada: {{ $submission->created_at->format('d F Y, H:i') }} WIB<br>
            ID Submission: #{{ $submission->id }}<br>
            @if($submission->metadata && isset($submission->metadata['ip']))
            IP Address: {{ $submission->metadata['ip'] }}<br>
            @endif
            @if($submission->metadata && isset($submission->metadata['user_agent']))
            User Agent: {{ $submission->metadata['user_agent'] }}
            @endif
        </div>

        <div class="footer">
            <p>
                <strong>Cara Membalas:</strong><br>
                Anda dapat membalas email ini secara langsung untuk mengirim balasan ke {{ $submission->name }}.
            </p>
            
            <p>
                Email ini dikirim otomatis dari sistem kontak {{ config('app.name') }}.<br>
                Jangan balas ke alamat ini jika Anda tidak bermaksud membalas ke pengirim.
            </p>
        </div>
    </div>
</body>
</html>
