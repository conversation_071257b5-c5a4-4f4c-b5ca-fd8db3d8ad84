import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, router } from '@inertiajs/react';
import { ArrowLeft, Download, Eye, Images, Trash2, Upload } from 'lucide-react';
import { useState } from 'react';

interface BusinessUnit {
    id: number;
    name: string;
    slug: string;
}

interface GalleryImage {
    id: number;
    name: string;
    file_name: string;
    mime_type: string;
    size: number;
    url: string;
    thumb_url: string;
    preview_url: string;
    created_at: string;
}

interface Props {
    business_unit: BusinessUnit;
    gallery_images: GalleryImage[];
}

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Admin', href: '/admin' },
    { title: 'Unit Bisnis', href: '/admin/business-units' },
    { title: 'Galeri', href: '#' },
];

export default function GalleryIndex({ business_unit, gallery_images }: Props) {
    const [selectedImages, setSelectedImages] = useState<number[]>([]);
    const [isUploading, setIsUploading] = useState(false);
    const [dragOver, setDragOver] = useState(false);

    // Removed unused deleteForm variable

    const handleImageSelect = (imageId: number, checked: boolean) => {
        if (checked) {
            setSelectedImages([...selectedImages, imageId]);
        } else {
            setSelectedImages(selectedImages.filter((id) => id !== imageId));
        }
    };

    const handleSelectAll = (checked: boolean) => {
        if (checked) {
            setSelectedImages(gallery_images.map((img) => img.id));
        } else {
            setSelectedImages([]);
        }
    };

    const handleBulkDelete = () => {
        if (selectedImages.length === 0) return;

        if (confirm(`Apakah Anda yakin ingin menghapus ${selectedImages.length} gambar?`)) {
            router.post(
                route('admin.business-units.gallery.bulk-delete', business_unit.slug),
                {
                    media_ids: selectedImages,
                },
                {
                    onSuccess: () => {
                        setSelectedImages([]);
                    },
                },
            );
        }
    };

    const handleFileUpload = async (files: FileList) => {
        if (files.length === 0) return;

        setIsUploading(true);
        const formData = new FormData();

        Array.from(files).forEach((file, index) => {
            formData.append(`files[${index}]`, file);
        });

        try {
            await router.post(route('admin.business-units.gallery.upload', business_unit.slug), formData, {
                forceFormData: true,
                onSuccess: () => {
                    setIsUploading(false);
                },
                onError: () => {
                    setIsUploading(false);
                },
            });
        } catch {
            setIsUploading(false);
        }
    };

    const handleDrop = (e: React.DragEvent) => {
        e.preventDefault();
        setDragOver(false);
        const files = e.dataTransfer.files;
        handleFileUpload(files);
    };

    const handleDragOver = (e: React.DragEvent) => {
        e.preventDefault();
        setDragOver(true);
    };

    const handleDragLeave = (e: React.DragEvent) => {
        e.preventDefault();
        setDragOver(false);
    };

    const formatFileSize = (bytes: number) => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Galeri - ${business_unit.name}`} />

            <div className="flex h-full flex-1 flex-col gap-4 p-4">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold tracking-tight">Galeri {business_unit.name}</h1>
                        <p className="text-muted-foreground">Kelola gambar untuk unit bisnis {business_unit.name}</p>
                    </div>
                    <div className="flex items-center gap-2">
                        {selectedImages.length > 0 && (
                            <Button variant="destructive" onClick={handleBulkDelete}>
                                <Trash2 className="mr-2 h-4 w-4" />
                                Hapus Terpilih ({selectedImages.length})
                            </Button>
                        )}
                        <Link href={`/admin/business-units/${business_unit.slug}`}>
                            <Button variant="outline">
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Kembali
                            </Button>
                        </Link>
                    </div>
                </div>

                {/* Upload Area */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Upload className="h-5 w-5" />
                            Upload Gambar
                        </CardTitle>
                        <CardDescription>
                            Drag & drop gambar atau klik untuk memilih file. Format yang didukung: JPEG, PNG, GIF, WebP (Max: 10MB)
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div
                            className={`rounded-lg border-2 border-dashed p-8 text-center transition-colors ${
                                dragOver ? 'border-blue-500 bg-blue-50' : 'border-gray-300'
                            } ${isUploading ? 'pointer-events-none opacity-50' : ''}`}
                            onDrop={handleDrop}
                            onDragOver={handleDragOver}
                            onDragLeave={handleDragLeave}
                        >
                            <Images className="mx-auto mb-4 h-12 w-12 text-gray-400" />
                            <p className="mb-2 text-lg font-medium text-gray-900">{isUploading ? 'Mengupload...' : 'Drop gambar di sini'}</p>
                            <p className="mb-4 text-sm text-gray-500">atau</p>
                            <input
                                type="file"
                                multiple
                                accept="image/*"
                                onChange={(e) => e.target.files && handleFileUpload(e.target.files)}
                                className="hidden"
                                id="file-upload"
                                disabled={isUploading}
                            />
                            <label htmlFor="file-upload">
                                <Button asChild disabled={isUploading}>
                                    <span>
                                        <Upload className="mr-2 h-4 w-4" />
                                        Pilih Gambar
                                    </span>
                                </Button>
                            </label>
                        </div>
                    </CardContent>
                </Card>

                {/* Gallery Grid */}
                <Card>
                    <CardHeader>
                        <div className="flex items-center justify-between">
                            <div>
                                <CardTitle className="flex items-center gap-2">
                                    <Images className="h-5 w-5" />
                                    Galeri Gambar
                                </CardTitle>
                                <CardDescription>Total {gallery_images.length} gambar</CardDescription>
                            </div>
                            {gallery_images.length > 0 && (
                                <div className="flex items-center gap-2">
                                    <Checkbox checked={selectedImages.length === gallery_images.length} onCheckedChange={handleSelectAll} />
                                    <span className="text-sm text-muted-foreground">Pilih Semua</span>
                                </div>
                            )}
                        </div>
                    </CardHeader>
                    <CardContent>
                        {gallery_images.length > 0 ? (
                            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                                {gallery_images.map((image) => (
                                    <div key={image.id} className="group relative">
                                        <div className="aspect-square overflow-hidden rounded-lg bg-gray-100">
                                            <img src={image.thumb_url} alt={image.name} className="h-full w-full object-cover" />
                                        </div>

                                        {/* Overlay */}
                                        <div className="bg-opacity-0 group-hover:bg-opacity-50 absolute inset-0 flex items-center justify-center rounded-lg bg-black transition-all duration-200">
                                            <div className="flex gap-2 opacity-0 transition-opacity duration-200 group-hover:opacity-100">
                                                <Button size="sm" variant="secondary" asChild>
                                                    <a href={image.url} target="_blank" rel="noopener noreferrer">
                                                        <Eye className="h-4 w-4" />
                                                    </a>
                                                </Button>
                                                <Button size="sm" variant="secondary" asChild>
                                                    <a href={image.url} download={image.file_name}>
                                                        <Download className="h-4 w-4" />
                                                    </a>
                                                </Button>
                                                <Button
                                                    size="sm"
                                                    variant="destructive"
                                                    onClick={() => {
                                                        if (confirm('Apakah Anda yakin ingin menghapus gambar ini?')) {
                                                            router.delete(
                                                                route('admin.business-units.gallery.destroy', [business_unit.slug, image.id]),
                                                            );
                                                        }
                                                    }}
                                                >
                                                    <Trash2 className="h-4 w-4" />
                                                </Button>
                                            </div>
                                        </div>

                                        {/* Selection Checkbox */}
                                        <div className="absolute top-2 left-2">
                                            <Checkbox
                                                checked={selectedImages.includes(image.id)}
                                                onCheckedChange={(checked) => handleImageSelect(image.id, checked as boolean)}
                                                className="bg-white"
                                            />
                                        </div>

                                        {/* Image Info */}
                                        <div className="mt-2">
                                            <p className="truncate text-sm font-medium">{image.name}</p>
                                            <div className="flex items-center justify-between text-xs text-muted-foreground">
                                                <span>{formatFileSize(image.size)}</span>
                                                <Badge variant="outline">{image.mime_type.split('/')[1].toUpperCase()}</Badge>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        ) : (
                            <div className="py-8 text-center">
                                <Images className="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
                                <h3 className="mb-2 text-lg font-medium text-muted-foreground">Belum ada gambar</h3>
                                <p className="text-sm text-muted-foreground">Upload gambar pertama untuk memulai galeri</p>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
