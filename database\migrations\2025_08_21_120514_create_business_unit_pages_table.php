<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('business_unit_pages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('business_unit_id')->constrained()->onDelete('cascade');
            $table->enum('type', ['hero', 'about', 'contact', 'pricing', 'catalog', 'menu', 'gallery', 'custom']);
            $table->string('title');
            $table->string('slug')->nullable(); // for custom pages
            $table->longText('content')->nullable();
            $table->json('images')->nullable(); // array of image paths
            $table->json('meta_data')->nullable(); // flexible data for different page types
            $table->json('seo_data')->nullable(); // meta title, description, keywords
            $table->boolean('is_active')->default(true);
            $table->integer('order')->default(0);
            $table->timestamps();

            $table->unique(['business_unit_id', 'type']); // one page per type per unit
            $table->index(['business_unit_id', 'is_active']);
            $table->index('type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('business_unit_pages');
    }
};
