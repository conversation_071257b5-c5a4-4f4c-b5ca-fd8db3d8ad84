<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class RoleAndPermissionSeeder extends Seeder
{
    public function run(): void
    {

        // Create permissions
        $permissions = [
            // Business Unit Management
            'view business units',
            'create business units',
            'edit business units',
            'delete business units',

            // Business Unit Pages Management
            'view business unit pages',
            'create business unit pages',
            'edit business unit pages',
            'delete business unit pages',

            // Gallery Management
            'view galleries',
            'create galleries',
            'edit galleries',
            'delete galleries',
            'upload galleries',

            // News Management
            'view news',
            'create news',
            'edit news',
            'delete news',
            'publish news',

            // Global Settings
            'view settings',
            'edit settings',

            // User Management (Super Admin only)
            'manage users',
            'view users',
            'create users',
            'edit users',
            'delete users',
            'assign roles',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Create roles and assign permissions

        // Super Admin - Full access
        $superAdmin = Role::firstOrCreate(['name' => 'super-admin']);
        $superAdmin->syncPermissions(Permission::all());

        // Admin - Content management only
        $admin = Role::firstOrCreate(['name' => 'admin']);
        $admin->syncPermissions([
            'view business units',
            'create business units',
            'edit business units',
            'delete business units',
            'view business unit pages',
            'create business unit pages',
            'edit business unit pages',
            'delete business unit pages',
            'view galleries',
            'create galleries',
            'edit galleries',
            'delete galleries',
            'upload galleries',
            'view news',
            'create news',
            'edit news',
            'delete news',
            'publish news',
            'view settings',
            'edit settings',
        ]);

        // Create default super admin user if it doesn't exist
        $superAdminUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Super Administrator',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
            ]
        );
        $superAdminUser->assignRole('super-admin');

        // Create default admin user if it doesn't exist
        $adminUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Administrator',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
            ]
        );
        $adminUser->assignRole('admin');
    }
}
