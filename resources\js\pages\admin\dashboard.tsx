import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/react';
import { BarChart3, Building2, FileText, Users } from 'lucide-react';

interface Stats {
    business_units: {
        total: number;
        active: number;
        coming_soon: number;
    };
    news: {
        total: number;
        published: number;
        draft: number;
    };
    users: {
        total: number;
        admins: number;
    };
}

interface NewsArticle {
    id: number;
    title: string;
    status: string;
    created_at: string;
    author: {
        name: string;
    };
}

interface BusinessUnit {
    id: number;
    name: string;
    slug: string;
    status: string;
    order: number;
}

interface Props {
    stats: Stats;
    recent_news: NewsArticle[];
    business_units: BusinessUnit[];
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Admin',
        href: '/admin',
    },
    {
        title: 'Dashboard',
        href: '/admin/dashboard',
    },
];

export default function AdminDashboard({ stats, recent_news, business_units }: Props) {
    const statCards = [
        {
            title: 'Unit Bisnis',
            value: stats.business_units.total,
            description: `${stats.business_units.active} aktif, ${stats.business_units.coming_soon} akan datang`,
            icon: Building2,
            color: 'text-blue-600',
        },
        {
            title: 'Artikel Berita',
            value: stats.news.total,
            description: `${stats.news.published} dipublikasi, ${stats.news.draft} draft`,
            icon: FileText,
            color: 'text-green-600',
        },
        {
            title: 'Pengguna',
            value: stats.users.total,
            description: `${stats.users.admins} admin`,
            icon: Users,
            color: 'text-purple-600',
        },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Dashboard Admin" />

            <div className="flex h-full flex-1 flex-col gap-4 p-4">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold tracking-tight">Dashboard Admin</h1>
                        <p className="text-muted-foreground">Kelola konten dan pengaturan website Cigi Global</p>
                    </div>
                    <BarChart3 className="h-8 w-8 text-muted-foreground" />
                </div>

                {/* Stats Cards */}
                <div className="grid gap-4 md:grid-cols-3">
                    {statCards.map((stat) => (
                        <Card key={stat.title}>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
                                <stat.icon className={`h-4 w-4 ${stat.color}`} />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{stat.value}</div>
                                <p className="text-xs text-muted-foreground">{stat.description}</p>
                            </CardContent>
                        </Card>
                    ))}
                </div>

                {/* Content Grid */}
                <div className="grid gap-4 md:grid-cols-2">
                    {/* Recent News */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Berita Terbaru</CardTitle>
                            <CardDescription>5 artikel berita terbaru</CardDescription>
                        </CardHeader>
                        <CardContent>
                            {recent_news.length > 0 ? (
                                <div className="space-y-3">
                                    {recent_news.map((article) => (
                                        <div key={article.id} className="flex items-start space-x-3 rounded-lg border p-3">
                                            <div className="min-w-0 flex-1">
                                                <p className="text-sm leading-none font-medium">{article.title}</p>
                                                <p className="text-xs text-muted-foreground">
                                                    oleh {article.author.name} • {new Date(article.created_at).toLocaleDateString('id-ID')}
                                                </p>
                                            </div>
                                            <div
                                                className={`rounded-full px-2 py-1 text-xs font-medium ${
                                                    article.status === 'published' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                                                }`}
                                            >
                                                {article.status === 'published' ? 'Dipublikasi' : 'Draft'}
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <p className="text-sm text-muted-foreground">Belum ada artikel berita.</p>
                            )}
                        </CardContent>
                    </Card>

                    {/* Business Units */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Unit Bisnis</CardTitle>
                            <CardDescription>Daftar semua unit bisnis</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3">
                                {business_units.map((unit) => (
                                    <div key={unit.id} className="flex items-center justify-between rounded-lg border p-3">
                                        <div>
                                            <p className="text-sm font-medium">{unit.name}</p>
                                            <p className="text-xs text-muted-foreground">/{unit.slug}</p>
                                        </div>
                                        <div
                                            className={`rounded-full px-2 py-1 text-xs font-medium ${
                                                unit.status === 'active'
                                                    ? 'bg-green-100 text-green-800'
                                                    : unit.status === 'coming_soon'
                                                      ? 'bg-blue-100 text-blue-800'
                                                      : 'bg-gray-100 text-gray-800'
                                            }`}
                                        >
                                            {unit.status === 'active' ? 'Aktif' : unit.status === 'coming_soon' ? 'Akan Datang' : 'Tidak Aktif'}
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
