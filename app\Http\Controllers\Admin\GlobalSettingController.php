<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\GlobalSetting;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class GlobalSettingController extends Controller
{
    use AuthorizesRequests;

    public function index(): Response
    {
        $this->authorize('view settings');

        // Group settings by group for better organization
        $settings = GlobalSetting::all()->groupBy('group');

        return Inertia::render('admin/settings/index', [
            'settings' => $settings,
        ]);
    }

    public function update(Request $request): RedirectResponse
    {
        $this->authorize('edit settings');

        $validated = $request->validate([
            'settings' => 'required|array',
            'settings.*' => 'nullable|string',
        ]);

        foreach ($validated['settings'] as $key => $value) {
            GlobalSetting::updateOrCreate(
                ['key' => $key],
                ['value' => $value]
            );
        }

        return redirect()
            ->route('admin.settings.index')
            ->with('success', 'Pengaturan berhasil disimpan.');
    }

    public function reset(): RedirectResponse
    {
        $this->authorize('edit settings');

        // Reset to default values
        $defaultSettings = [
            // Site Information
            'site_title' => 'Cigi Global',
            'site_description' => 'Pusat bisnis terpadu yang mengembangkan berbagai unit usaha untuk kemajuan masyarakat desa',
            'site_keywords' => 'cigi global, bisnis, desa, unit usaha, badminton, internet, retail, kuliner, sepak bola, pertanian, panahan',

            // Homepage Hero
            'hero_title' => 'Selamat Datang di Cigi Global',
            'hero_subtitle' => 'Pusat Bisnis Terpadu',
            'hero_description' => 'Mengembangkan berbagai unit usaha untuk kemajuan masyarakat desa dengan inovasi dan dedikasi tinggi',
            'hero_image' => '',

            // About Section
            'about_title' => 'Tentang Cigi Global',
            'about_description' => 'Cigi Global adalah pusat bisnis terpadu yang berkomitmen mengembangkan berbagai unit usaha untuk kemajuan dan kesejahteraan masyarakat desa.',

            // Contact Information
            'contact_phone' => '',
            'contact_email' => '',
            'contact_address' => '',
            'contact_whatsapp' => '',

            // Social Media
            'social_instagram' => '',
            'social_facebook' => '',
            'social_youtube' => '',
            'social_tiktok' => '',

            // SEO Settings
            'google_analytics_id' => '',
            'google_tag_manager_id' => '',
            'facebook_pixel_id' => '',

            // Email Settings
            'admin_email' => '',
            'contact_form_email' => '',

            // Maintenance
            'maintenance_mode' => 'false',
            'maintenance_message' => 'Website sedang dalam pemeliharaan. Silakan kembali lagi nanti.',
        ];

        foreach ($defaultSettings as $key => $value) {
            GlobalSetting::updateOrCreate(
                ['key' => $key],
                [
                    'value' => $value,
                    'group' => $this->getGroupForKey($key),
                    'type' => $this->getTypeForKey($key),
                    'description' => $this->getDescriptionForKey($key),
                ]
            );
        }

        return redirect()
            ->route('admin.settings.index')
            ->with('success', 'Pengaturan berhasil direset ke nilai default.');
    }

    private function getGroupForKey(string $key): string
    {
        if (str_starts_with($key, 'site_')) {
            return 'site';
        }
        if (str_starts_with($key, 'hero_')) {
            return 'homepage';
        }
        if (str_starts_with($key, 'about_')) {
            return 'homepage';
        }
        if (str_starts_with($key, 'contact_')) {
            return 'contact';
        }
        if (str_starts_with($key, 'social_')) {
            return 'social';
        }
        if (str_contains($key, 'google') || str_contains($key, 'facebook') || str_contains($key, 'analytics')) {
            return 'seo';
        }
        if (str_contains($key, 'email')) {
            return 'email';
        }
        if (str_starts_with($key, 'maintenance_')) {
            return 'maintenance';
        }

        return 'general';
    }

    private function getTypeForKey(string $key): string
    {
        if (str_ends_with($key, '_image')) {
            return 'image';
        }
        if (str_ends_with($key, '_email')) {
            return 'email';
        }
        if (str_ends_with($key, '_phone') || str_ends_with($key, '_whatsapp')) {
            return 'tel';
        }
        if (str_contains($key, 'description') || str_contains($key, 'message')) {
            return 'textarea';
        }
        if ($key === 'maintenance_mode') {
            return 'boolean';
        }

        return 'text';
    }

    private function getDescriptionForKey(string $key): string
    {
        $descriptions = [
            'site_title' => 'Judul website yang akan muncul di tab browser',
            'site_description' => 'Deskripsi website untuk SEO',
            'site_keywords' => 'Kata kunci untuk SEO, pisahkan dengan koma',
            'hero_title' => 'Judul utama di halaman beranda',
            'hero_subtitle' => 'Subjudul di halaman beranda',
            'hero_description' => 'Deskripsi di bagian hero halaman beranda',
            'hero_image' => 'URL gambar latar belakang hero',
            'about_title' => 'Judul bagian tentang di beranda',
            'about_description' => 'Deskripsi tentang Cigi Global',
            'contact_phone' => 'Nomor telepon utama',
            'contact_email' => 'Email kontak utama',
            'contact_address' => 'Alamat lengkap',
            'contact_whatsapp' => 'Nomor WhatsApp',
            'social_instagram' => 'Username Instagram (tanpa @)',
            'social_facebook' => 'Nama halaman Facebook',
            'social_youtube' => 'Channel YouTube',
            'social_tiktok' => 'Username TikTok (tanpa @)',
            'google_analytics_id' => 'Google Analytics Tracking ID',
            'google_tag_manager_id' => 'Google Tag Manager ID',
            'facebook_pixel_id' => 'Facebook Pixel ID',
            'admin_email' => 'Email administrator',
            'contact_form_email' => 'Email penerima form kontak',
            'maintenance_mode' => 'Mode pemeliharaan website',
            'maintenance_message' => 'Pesan yang ditampilkan saat maintenance',
        ];

        return $descriptions[$key] ?? '';
    }
}
