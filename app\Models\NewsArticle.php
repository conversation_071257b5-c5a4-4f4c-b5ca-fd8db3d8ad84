<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class NewsArticle extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'slug',
        'excerpt',
        'content',
        'featured_image',
        'gallery',
        'status',
        'published_at',
        'created_by',
        'tags',
        'seo_data',
        'views',
        'is_featured',
    ];

    protected function casts(): array
    {
        return [
            'gallery' => 'array',
            'tags' => 'array',
            'seo_data' => 'array',
            'views' => 'integer',
            'is_featured' => 'boolean',
            'published_at' => 'datetime',
        ];
    }

    public function author(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function scopePublished($query)
    {
        return $query->where('status', 'published')
            ->where('published_at', '<=', now());
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    public function scopeLatest($query)
    {
        return $query->orderBy('published_at', 'desc');
    }

    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    public function isPublished(): bool
    {
        return $this->status === 'published' &&
               $this->published_at &&
               $this->published_at->isPast();
    }

    public function getMetaTitle(): ?string
    {
        return $this->seo_data['title'] ?? $this->title;
    }

    public function getMetaDescription(): ?string
    {
        return $this->seo_data['description'] ?? $this->excerpt;
    }

    public function getMetaKeywords(): ?string
    {
        return $this->seo_data['keywords'] ?? implode(', ', $this->tags ?? []);
    }

    public function incrementViews(): void
    {
        $this->increment('views');
    }

    public function getReadingTime(): int
    {
        $wordCount = str_word_count(strip_tags($this->content));

        return ceil($wordCount / 200); // Average reading speed
    }
}
