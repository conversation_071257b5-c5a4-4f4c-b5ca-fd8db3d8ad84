<?php

use App\Models\User;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

beforeEach(function () {
    // Run migrations for permission tables
    $this->artisan('migrate:fresh');

    // Create permissions
    Permission::create(['name' => 'manage users']);

    // Create roles
    $superAdminRole = Role::create(['name' => 'super-admin']);
    $adminRole = Role::create(['name' => 'admin']);

    // Assign permissions
    $superAdminRole->givePermissionTo('manage users');

    // Create a super admin user for testing
    $this->superAdmin = User::factory()->create();
    $this->superAdmin->assignRole('super-admin');

    // Create a regular admin user
    $this->admin = User::factory()->create();
    $this->admin->assignRole('admin');
});

it('allows super admin to access user management index', function () {
    $response = $this->actingAs($this->superAdmin)
        ->get(route('admin.users.index'));

    $response->assertStatus(200);
    $response->assertInertia(fn ($page) => $page->component('admin/users/index'));
});

it('prevents regular admin from accessing user management', function () {
    $response = $this->actingAs($this->admin)
        ->get(route('admin.users.index'));

    $response->assertStatus(403);
});

it('allows super admin to create new users', function () {
    $userData = [
        'name' => 'Test User',
        'email' => '<EMAIL>',
        'password' => 'password123',
        'password_confirmation' => 'password123',
        'role' => 'admin',
    ];

    $response = $this->actingAs($this->superAdmin)
        ->post(route('admin.users.store'), $userData);

    $response->assertRedirect(route('admin.users.index'));

    $this->assertDatabaseHas('users', [
        'name' => 'Test User',
        'email' => '<EMAIL>',
    ]);

    $user = User::where('email', '<EMAIL>')->first();
    expect($user->hasRole('admin'))->toBeTrue();
});

it('validates user creation data', function () {
    $response = $this->actingAs($this->superAdmin)
        ->post(route('admin.users.store'), [
            'name' => '',
            'email' => 'invalid-email',
            'password' => '123',
            'password_confirmation' => '456',
            'role' => 'invalid-role',
        ]);

    $response->assertSessionHasErrors(['name', 'email', 'password', 'role']);
});

it('allows super admin to update users', function () {
    $user = User::factory()->create();
    $user->assignRole('admin');

    $updateData = [
        'name' => 'Updated Name',
        'email' => '<EMAIL>',
        'role' => 'admin',
    ];

    $response = $this->actingAs($this->superAdmin)
        ->put(route('admin.users.update', $user), $updateData);

    $response->assertRedirect(route('admin.users.index'));

    $user->refresh();
    expect($user->name)->toBe('Updated Name');
    expect($user->email)->toBe('<EMAIL>');
});

it('allows super admin to delete users except super admin', function () {
    $user = User::factory()->create();
    $user->assignRole('admin');

    $response = $this->actingAs($this->superAdmin)
        ->delete(route('admin.users.destroy', $user));

    $response->assertRedirect(route('admin.users.index'));
    $this->assertDatabaseMissing('users', ['id' => $user->id]);
});

it('prevents deleting super admin users', function () {
    $superAdminUser = User::factory()->create();
    $superAdminUser->assignRole('super-admin');

    $response = $this->actingAs($this->superAdmin)
        ->delete(route('admin.users.destroy', $superAdminUser));

    $response->assertRedirect(route('admin.users.index'));
    $this->assertDatabaseHas('users', ['id' => $superAdminUser->id]);
});

it('prevents users from deleting themselves', function () {
    $response = $this->actingAs($this->superAdmin)
        ->delete(route('admin.users.destroy', $this->superAdmin));

    $response->assertRedirect(route('admin.users.index'));
    $this->assertDatabaseHas('users', ['id' => $this->superAdmin->id]);
});

it('shows user details to super admin', function () {
    $user = User::factory()->create();
    $user->assignRole('admin');

    $response = $this->actingAs($this->superAdmin)
        ->get(route('admin.users.show', $user));

    $response->assertStatus(200);
    $response->assertInertia(fn ($page) => $page->component('admin/users/show'));
});

it('shows user edit form to super admin', function () {
    $user = User::factory()->create();
    $user->assignRole('admin');

    $response = $this->actingAs($this->superAdmin)
        ->get(route('admin.users.edit', $user));

    $response->assertStatus(200);
    $response->assertInertia(fn ($page) => $page->component('admin/users/edit'));
});
