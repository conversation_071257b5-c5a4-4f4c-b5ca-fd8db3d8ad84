import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, router } from '@inertiajs/react';
import { Eye, Mail, Search } from 'lucide-react';
import { useState } from 'react';

interface ContactSubmission {
    id: number;
    name: string;
    email: string;
    phone?: string;
    subject: string;
    message: string;
    status: string;
    business_unit: {
        id: number;
        name: string;
        slug: string;
    };
    created_at: string;
    read_at?: string;
    replied_at?: string;
}

interface BusinessUnit {
    id: number;
    name: string;
}

interface PaginatedSubmissions {
    data: ContactSubmission[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    links: Array<{
        url: string | null;
        label: string;
        active: boolean;
    }>;
}

interface Props {
    submissions: PaginatedSubmissions;
    business_units: BusinessUnit[];
    search: string;
    business_unit_filter: string;
    status_filter: string;
}

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Admin', href: '/admin' },
    { title: 'Pesan Kontak', href: '/admin/contact-submissions' },
];

export default function ContactSubmissionsIndex({ submissions, business_units, search, business_unit_filter, status_filter }: Props) {
    const [searchTerm, setSearchTerm] = useState(search);
    const [businessUnitFilter, setBusinessUnitFilter] = useState(business_unit_filter || 'all');
    const [statusFilter, setStatusFilter] = useState(status_filter || 'all');

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();
        router.get(
            route('admin.contact-submissions.index'),
            {
                search: searchTerm,
                business_unit: businessUnitFilter === 'all' ? '' : businessUnitFilter,
                status: statusFilter === 'all' ? '' : statusFilter,
            },
            { preserveState: true },
        );
    };

    const handleBusinessUnitFilter = (value: string) => {
        setBusinessUnitFilter(value);
        router.get(
            route('admin.contact-submissions.index'),
            {
                search: searchTerm,
                business_unit: value === 'all' ? '' : value,
                status: statusFilter === 'all' ? '' : statusFilter,
            },
            { preserveState: true },
        );
    };

    const handleStatusFilter = (value: string) => {
        setStatusFilter(value);
        router.get(
            route('admin.contact-submissions.index'),
            {
                search: searchTerm,
                business_unit: businessUnitFilter === 'all' ? '' : businessUnitFilter,
                status: value === 'all' ? '' : value,
            },
            { preserveState: true },
        );
    };

    const getStatusBadgeVariant = (status: string) => {
        switch (status) {
            case 'new':
                return 'destructive';
            case 'read':
                return 'default';
            case 'replied':
                return 'secondary';
            default:
                return 'outline';
        }
    };

    const getStatusLabel = (status: string) => {
        switch (status) {
            case 'new':
                return 'Baru';
            case 'read':
                return 'Dibaca';
            case 'replied':
                return 'Dibalas';
            default:
                return status;
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Pesan Kontak" />

            <div className="flex h-full flex-1 flex-col gap-4 p-4">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold tracking-tight">Pesan Kontak</h1>
                        <p className="text-muted-foreground">Kelola pesan kontak dari semua unit bisnis</p>
                    </div>
                </div>

                {/* Search and Filters */}
                <Card>
                    <CardContent className="pt-6">
                        <form onSubmit={handleSearch} className="flex gap-2">
                            <Input
                                placeholder="Cari pesan..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                className="flex-1"
                            />
                            <Select value={businessUnitFilter} onValueChange={handleBusinessUnitFilter}>
                                <SelectTrigger className="w-48">
                                    <SelectValue placeholder="Filter Unit Bisnis" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">Semua Unit Bisnis</SelectItem>
                                    <SelectItem value="global">Cigi Global</SelectItem>
                                    {business_units.map((unit) => (
                                        <SelectItem key={unit.id} value={unit.id.toString()}>
                                            {unit.name}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                            <Select value={statusFilter} onValueChange={handleStatusFilter}>
                                <SelectTrigger className="w-32">
                                    <SelectValue placeholder="Status" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">Semua Status</SelectItem>
                                    <SelectItem value="new">Baru</SelectItem>
                                    <SelectItem value="read">Dibaca</SelectItem>
                                    <SelectItem value="replied">Dibalas</SelectItem>
                                </SelectContent>
                            </Select>
                            <Button type="submit" variant="outline">
                                <Search className="h-4 w-4" />
                            </Button>
                        </form>
                    </CardContent>
                </Card>

                {/* Submissions Table */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Mail className="h-5 w-5" />
                            Daftar Pesan Kontak
                        </CardTitle>
                        <CardDescription>Total {submissions.total} pesan kontak</CardDescription>
                    </CardHeader>
                    <CardContent>
                        {submissions.data.length > 0 ? (
                            <div className="space-y-4">
                                {submissions.data.map((submission) => (
                                    <div key={submission.id} className="flex items-center justify-between rounded-lg border p-4">
                                        <div className="flex-1">
                                            <div className="mb-2 flex items-center gap-3">
                                                <h3 className="font-medium">{submission.name}</h3>
                                                <Badge variant={getStatusBadgeVariant(submission.status)}>{getStatusLabel(submission.status)}</Badge>
                                                <span className="text-sm text-muted-foreground">{submission.business_unit.name}</span>
                                            </div>
                                            <p className="mb-1 text-sm font-medium">{submission.subject}</p>
                                            <p className="mb-1 text-sm text-muted-foreground">{submission.email}</p>
                                            <p className="text-xs text-muted-foreground">
                                                {new Date(submission.created_at).toLocaleDateString('id-ID', {
                                                    year: 'numeric',
                                                    month: 'long',
                                                    day: 'numeric',
                                                    hour: '2-digit',
                                                    minute: '2-digit',
                                                })}
                                            </p>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <Link href={route('admin.contact-submissions.show', submission.id)}>
                                                <Button variant="outline" size="sm">
                                                    <Eye className="h-4 w-4" />
                                                    Lihat
                                                </Button>
                                            </Link>
                                        </div>
                                    </div>
                                ))}

                                {/* Pagination */}
                                {submissions.last_page > 1 && (
                                    <div className="mt-6 flex justify-center">
                                        <div className="flex items-center gap-2">
                                            {submissions.links.map((link, index) => (
                                                <Button
                                                    key={index}
                                                    variant={link.active ? 'default' : 'outline'}
                                                    size="sm"
                                                    disabled={!link.url}
                                                    onClick={() => link.url && router.get(link.url)}
                                                    dangerouslySetInnerHTML={{ __html: link.label }}
                                                />
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </div>
                        ) : (
                            <div className="py-8 text-center">
                                <Mail className="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
                                <h3 className="mb-2 text-lg font-medium text-muted-foreground">
                                    {search || business_unit_filter || status_filter ? 'Tidak ada hasil' : 'Belum ada pesan kontak'}
                                </h3>
                                <p className="text-sm text-muted-foreground">
                                    {search || business_unit_filter || status_filter
                                        ? 'Coba ubah filter pencarian Anda'
                                        : 'Pesan kontak akan muncul di sini ketika ada yang mengirim'}
                                </p>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
