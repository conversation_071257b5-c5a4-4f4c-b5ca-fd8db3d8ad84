import { useState, useEffect, useRef, useMemo } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { useScrollAnimation } from '../hooks/useScrollAnimation'
import DataImage from '../data'

const Navbar = () => {
  const [isScrolled, setIsScrolled] = useState(false)
  const [dropdownOpen, setDropdownOpen] = useState(false)
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const dropdownRef = useRef(null)
  const location = useLocation()
  const { scrollY, scrollDirection } = useScrollAnimation()

  // Handle scroll effect with improved logic
  useEffect(() => {
    setIsScrolled(scrollY > 50)
  }, [scrollY])

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setDropdownOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  // Close mobile menu on route change
  useEffect(() => {
    setMobileMenuOpen(false)
    setDropdownOpen(false)
  }, [location])

  // Hide navbar on scroll down, show on scroll up
  const shouldHideNavbar = scrollDirection === 'down' && scrollY > 100 && !mobileMenuOpen

  // Unit Usaha configuration with actual logos and updated info
  const unitUsaha = useMemo(
    () => [
      {
        name: 'Semua Unit Usaha',
        subtitle: 'Lihat Semua',
        path: '/usaha',
        logo: DataImage.CigiGlobal2,
        theme: 'default',
      },
      {
        name: 'CigiFarm',
        subtitle: 'Peternakan ayam petelur & budidaya ikan lele',
        path: '/cigifarm',
        logo: DataImage.CigiFarm,
        theme: 'green',
        brandName: 'CigiFarm',
        description: 'Peternakan & Budidaya',
      },
      {
        name: 'CigiNet',
        subtitle: 'Layanan internet fiber optik & ISP lokal',
        path: '/ciginet',
        logo: DataImage.CigiNet,
        theme: 'blue',
        brandName: 'CigiNet',
        description: 'Internet & ISP Lokal',
      },
      {
        name: 'CigiMart',
        subtitle: 'Retail dan marketplace',
        path: '/cigimart',
        logo: DataImage.CigiMart,
        theme: 'orange',
        brandName: 'CigiMart',
        description: 'Retail & Marketplace',
      },
      {
        name: 'CigiFood',
        subtitle: 'Produksi makanan & olahan pangan',
        path: '/cigifood',
        logo: DataImage.CigiFood,
        theme: 'red',
        brandName: 'CigiFood',
        description: 'Produksi Makanan & Olahan Pangan',
      },
      {
        name: 'PB Cigi',
        subtitle: 'Persatuan badminton Cimande Girang',
        path: '/pbcigi',
        logo: DataImage.PBcigi,
        theme: 'purple',
        brandName: 'PB Cigi',
        description: 'Persatuan Badminton Cimande Girang',
      },
      {
        name: 'CigiArchery',
        subtitle: 'Klub panahan Cimande Girang',
        path: '/cigiarchery',
        logo: DataImage.CigiArchery,
        theme: 'yellow',
        brandName: 'CigiArchery',
        description: 'Klub Panahan Cimande Girang',
      },
      {
        name: 'CigiFC',
        subtitle: 'Klub sepak bola & kegiatan olahraga',
        path: '/cigifc',
        logo: DataImage.CigiFC,
        theme: 'indigo',
        brandName: 'CigiFC',
        description: 'Klub Sepak Bola & Kegiatan Olahraga',
      },
      {
        name: 'KRL Cigi',
        subtitle: 'Layanan transportasi kereta ringan lokal',
        path: '/krlcigi',
        logo: DataImage.KRLCigi,
        theme: 'gray',
        brandName: 'KRL Cigi',
        description: 'Transportasi Kereta Ringan Lokal',
      },
    ],
    []
  )

  // Get current unit usaha based on path
  const currentUnit = useMemo(() => {
    return (
      unitUsaha.find(
        (unit) =>
          location.pathname === unit.path ||
          (unit.path !== '/usaha' && location.pathname.startsWith(unit.path))
      ) || null
    )
  }, [location.pathname, unitUsaha])

  // Navigation links - consistent across all pages but content changes
  const navLinks = [
    { name: 'Beranda', path: '/' },
    { name: 'Tentang Kami', path: '/about' },
    { name: 'Berita', path: '/news' }, // Will become "Produk" on unit pages
    { name: 'Kontak', path: '/contact' },
  ]

  // Dynamic content based on current unit - navbar structure stays the same
  const getContentBasedOnUnit = () => {
    if (currentUnit && currentUnit.path !== '/usaha' && currentUnit.path !== '/') {
      // When on a specific unit page, navigation scrolls to sections
      return {
        berandaPath: currentUnit.path, // Refresh current page
        aboutPath: '#about',
        aboutText: `Tentang ${currentUnit.brandName}`,
        newsPath: '#product', // Changed to product section
        newsText: 'Produk',
        contactPath: '#contact',
        showUnitContent: true,
        isScrollNavigation: true,
      }
    }

    // Default content for home and general pages
    return {
      berandaPath: '/',
      aboutPath: '/about',
      aboutText: 'Tentang Kami',
      newsPath: '/news',
      newsText: 'Berita',
      contactPath: '/contact',
      showUnitContent: false,
      isScrollNavigation: false,
    }
  }

  const contentConfig = getContentBasedOnUnit()

  // Smooth scroll function for section navigation
  const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId.replace('#', ''))
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
        inline: 'nearest',
      })
    }
  }

  // Handle refresh and scroll to top
  const handleBerandaClick = (e) => {
    if (contentConfig.isScrollNavigation) {
      e.preventDefault()
      window.location.reload()
      setTimeout(() => {
        window.scrollTo({ top: 0, behavior: 'smooth' })
      }, 100)
    }
    // For mobile menu, also close it
    if (mobileMenuOpen) {
      setMobileMenuOpen(false)
    }
  }

  // Handle navigation clicks
  const handleNavClick = (e, path, text, linkName) => {
    if (linkName === 'Beranda' && contentConfig.isScrollNavigation) {
      return handleBerandaClick(e, path)
    }

    if (contentConfig.isScrollNavigation && path.startsWith('#')) {
      e.preventDefault()
      scrollToSection(path)
    }
    // For mobile menu, also close it
    if (mobileMenuOpen) {
      setMobileMenuOpen(false)
    }
  }

  // Get brand configuration
  const getBrandConfig = () => {
    if (currentUnit && currentUnit.path !== '/usaha') {
      return {
        name: currentUnit.brandName,
        subtitle: currentUnit.description,
        logo: DataImage.CigiGlobal2, // Could be customized per unit
        theme: currentUnit.theme,
      }
    }

    return {
      name: 'CigiGlobal',
      subtitle: 'Inovasi • Teknologi • Keunggulan',
      logo: DataImage.CigiGlobal2,
      theme: 'default',
    }
  }

  const brandConfig = getBrandConfig()

  const isActivePath = (path) => location.pathname === path

  return (
    <>
      {/* Main Navbar */}
      <nav
        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ${
          shouldHideNavbar ? '-translate-y-full' : 'translate-y-0'
        } ${
          isScrolled
            ? 'bg-zinc-900/95 backdrop-blur-xl shadow-2xl border-b border-zinc-700/30'
            : 'bg-transparent'
        }`}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Dynamic Logo */}
            <div className="flex-shrink-0">
              <Link
                to={currentUnit && currentUnit.path !== '/usaha' ? currentUnit.path : '/'}
                className="group flex items-center space-x-3 text-2xl font-bold text-white hover:text-amber-400 transition-all duration-300"
              >
                <div className="flex items-center space-x-2">
                  <img
                    src={brandConfig.logo}
                    alt={`${brandConfig.name} Logo`}
                    className="w-10 h-10 rounded-md object-cover group-hover:scale-110 transition-transform duration-300"
                    loading="lazy"
                  />
                  {/* Show unit-specific logo when on unit page */}
                  {currentUnit && currentUnit.path !== '/usaha' && currentUnit.path !== '/' && (
                    <img
                      src={currentUnit.logo}
                      alt={`${currentUnit.brandName} Logo`}
                      className="w-8 h-8 rounded-md object-cover group-hover:scale-110 transition-transform duration-300"
                      loading="lazy"
                    />
                  )}
                </div>
                <div className="flex flex-col">
                  <span className="leading-tight">
                    {brandConfig.name === 'CigiGlobal' ? (
                      <>
                        Cigi<span className="text-amber-500">Global</span>
                      </>
                    ) : (
                      <span className="text-amber-400">{brandConfig.name}</span>
                    )}
                  </span>
                  {currentUnit && currentUnit.path !== '/usaha' && currentUnit.path !== '/' && (
                    <span className="text-xs text-zinc-400 font-normal leading-tight">
                      {brandConfig.subtitle}
                    </span>
                  )}
                </div>
              </Link>
            </div>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-8">
              {/* First nav item (Beranda) */}
              {contentConfig.isScrollNavigation ? (
                <button
                  onClick={(e) =>
                    handleNavClick(e, contentConfig.berandaPath, navLinks[0].name, navLinks[0].name)
                  }
                  className={`relative px-3 py-2 text-sm font-medium transition-all duration-200 group ${
                    isActivePath(navLinks[0].path)
                      ? 'text-amber-400'
                      : 'text-white hover:text-amber-400'
                  }`}
                >
                  {navLinks[0].name}
                  <span
                    className={`absolute bottom-0 left-0 h-0.5 bg-amber-400 transition-all duration-200 ${
                      isActivePath(navLinks[0].path) ? 'w-full' : 'w-0 group-hover:w-full'
                    }`}
                  ></span>
                </button>
              ) : (
                <Link
                  to={navLinks[0].path}
                  className={`relative px-3 py-2 text-sm font-medium transition-all duration-200 group ${
                    isActivePath(navLinks[0].path)
                      ? 'text-amber-400'
                      : 'text-white hover:text-amber-400'
                  }`}
                >
                  {navLinks[0].name}
                  <span
                    className={`absolute bottom-0 left-0 h-0.5 bg-amber-400 transition-all duration-200 ${
                      isActivePath(navLinks[0].path) ? 'w-full' : 'w-0 group-hover:w-full'
                    }`}
                  ></span>
                </Link>
              )}

              {/* Unit Usaha Dropdown (positioned after Beranda) */}
              <div className="relative" ref={dropdownRef}>
                <button
                  onClick={() => setDropdownOpen(!dropdownOpen)}
                  className={`flex items-center px-3 py-2 text-sm font-medium transition-all duration-200 group ${
                    location.pathname.startsWith('/usaha') ||
                    location.pathname.startsWith('/Detailusaha')
                      ? 'text-amber-400'
                      : 'text-white hover:text-amber-400'
                  }`}
                >
                  Unit Usaha
                  <svg
                    className={`ml-2 h-4 w-4 transition-transform duration-200 ${
                      dropdownOpen ? 'rotate-180' : ''
                    }`}
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                  <span
                    className={`absolute bottom-0 left-0 h-0.5 bg-amber-400 transition-all duration-200 ${
                      location.pathname.startsWith('/usaha') ||
                      location.pathname.startsWith('/Detailusaha')
                        ? 'w-full'
                        : 'w-0 group-hover:w-full'
                    }`}
                  ></span>
                </button>

                {/* Dropdown Menu */}
                {dropdownOpen && (
                  <div className="absolute top-full right-0 mt-3 w-[min(95vw,56rem)] animate-fade-in-scale">
                    <div className="glass-effect rounded-xl shadow-2xl mx-4 max-h-[75vh] overflow-auto">
                      <div className="p-6">
                        <h3 className="text-white font-semibold mb-4 text-center">
                          Pilih Unit Usaha
                        </h3>
                        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
                          {unitUsaha.map((unit, index) => (
                            <Link
                              key={index}
                              to={unit.path}
                              onClick={() => setDropdownOpen(false)}
                              className={`flex items-start p-4 rounded-lg transition-all duration-300 group hover:scale-105 ${
                                location.pathname === unit.path
                                  ? 'bg-amber-600/20 border border-amber-600/30'
                                  : 'hover:bg-zinc-700/50'
                              }`}
                            >
                              <div className="flex-shrink-0 mr-3">
                                <div
                                  className={`w-12 h-12 rounded-lg overflow-hidden transition-all duration-300 ${
                                    location.pathname === unit.path
                                      ? 'ring-2 ring-amber-600'
                                      : 'group-hover:ring-2 group-hover:ring-amber-600'
                                  }`}
                                >
                                  <img
                                    src={unit.logo}
                                    alt={`${unit.name} Logo`}
                                    className="w-full h-full object-cover"
                                    loading="lazy"
                                  />
                                </div>
                              </div>
                              <div className="flex-1 min-w-0">
                                <h4
                                  className={`font-semibold transition-colors text-sm ${
                                    location.pathname === unit.path
                                      ? 'text-amber-400'
                                      : 'text-white group-hover:text-amber-400'
                                  }`}
                                >
                                  {unit.name}
                                </h4>
                                <p className="text-zinc-400 text-xs mt-1 group-hover:text-zinc-300 transition-colors line-clamp-2">
                                  {unit.subtitle}
                                </p>
                              </div>
                            </Link>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Remaining nav items with dynamic content */}
              {navLinks.slice(1).map((link) => {
                // Make navigation dynamic based on current unit
                let linkPath = link.path
                let linkText = link.name

                if (link.name === 'Tentang Kami') {
                  linkPath = contentConfig.aboutPath
                  linkText = contentConfig.aboutText
                } else if (link.name === 'Berita') {
                  linkPath = contentConfig.newsPath
                  linkText = contentConfig.newsText
                } else if (link.name === 'Kontak') {
                  linkPath = contentConfig.contactPath
                }

                const isActive =
                  isActivePath(linkPath) ||
                  (link.name === 'Tentang Kami' &&
                    contentConfig.showUnitContent &&
                    isActivePath(currentUnit?.path))

                return contentConfig.isScrollNavigation && linkPath.startsWith('#') ? (
                  <button
                    key={link.path}
                    onClick={(e) => handleNavClick(e, linkPath, linkText, link.name)}
                    className={`relative px-3 py-2 text-sm font-medium transition-all duration-200 group ${
                      isActive ? 'text-amber-400' : 'text-white hover:text-amber-400'
                    }`}
                  >
                    {linkText}
                    <span
                      className={`absolute bottom-0 left-0 h-0.5 bg-amber-400 transition-all duration-200 ${
                        isActive ? 'w-full' : 'w-0 group-hover:w-full'
                      }`}
                    ></span>
                  </button>
                ) : (
                  <Link
                    key={link.path}
                    to={linkPath}
                    className={`relative px-3 py-2 text-sm font-medium transition-all duration-200 group ${
                      isActive ? 'text-amber-400' : 'text-white hover:text-amber-400'
                    }`}
                  >
                    {linkText}
                    <span
                      className={`absolute bottom-0 left-0 h-0.5 bg-amber-400 transition-all duration-200 ${
                        isActive ? 'w-full' : 'w-0 group-hover:w-full'
                      }`}
                    ></span>
                  </Link>
                )
              })}
            </div>

            {/* Mobile menu button */}
            <div className="md:hidden">
              <button
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                className="inline-flex items-center justify-center p-2 rounded-md text-white hover:text-amber-400 hover:bg-zinc-700/50 transition-all duration-200"
              >
                <svg
                  className={`h-6 w-6 transition-transform duration-200 ${
                    mobileMenuOpen ? 'rotate-90' : ''
                  }`}
                  stroke="currentColor"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  {mobileMenuOpen ? (
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  ) : (
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 6h16M4 12h16M4 18h16"
                    />
                  )}
                </svg>
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* Mobile Menu */}
      <div
        className={`fixed inset-y-0 right-0 z-50 w-full max-w-xs transform transition-all duration-500 ease-out md:hidden ${
          mobileMenuOpen ? 'translate-x-0' : 'translate-x-full'
        }`}
      >
        <div className="flex flex-col h-full glass-effect border-l border-zinc-700/50">
          {/* Mobile menu header */}
          <div className="flex items-center justify-between px-4 py-4 border-b border-zinc-700/50">
            <Link
              to={currentUnit && currentUnit.path !== '/usaha' ? currentUnit.path : '/'}
              className="flex items-center space-x-2 text-lg font-bold text-white"
              onClick={() => setMobileMenuOpen(false)}
            >
              <div className="flex items-center space-x-2">
                <img
                  src={brandConfig.logo}
                  alt={`${brandConfig.name} Logo`}
                  className="w-6 h-6 rounded-md object-cover"
                  loading="lazy"
                />
                {currentUnit && currentUnit.path !== '/usaha' && currentUnit.path !== '/' && (
                  <img
                    src={currentUnit.logo}
                    alt={`${currentUnit.brandName} Logo`}
                    className="w-5 h-5 rounded-md object-cover"
                    loading="lazy"
                  />
                )}
              </div>
              <div className="flex flex-col">
                {currentUnit && currentUnit.path !== '/usaha' && (
                  <span className="text-xs text-zinc-400 font-normal leading-tight">
                    {brandConfig.subtitle}
                  </span>
                )}
              </div>
            </Link>
            <button
              onClick={() => setMobileMenuOpen(false)}
              className="p-2 rounded-md text-white hover:text-amber-400 transition-colors"
            >
              <svg className="h-5 w-5" stroke="currentColor" fill="none" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>

          {/* Mobile menu content */}
          <div className="flex-1 overflow-y-auto px-4 py-4">
            <div className="space-y-4">
              {/* Navigation Links - Reordered */}
              <div className="space-y-2">
                {/* Beranda */}
                {contentConfig.isScrollNavigation ? (
                  <button
                    onClick={(e) =>
                      handleNavClick(
                        e,
                        contentConfig.berandaPath,
                        navLinks[0].name,
                        navLinks[0].name
                      )
                    }
                    className={`block w-full text-left px-3 py-2 text-base font-medium rounded-lg transition-colors ${
                      isActivePath(navLinks[0].path)
                        ? 'text-amber-400 bg-amber-400/10'
                        : 'text-white hover:text-amber-400 hover:bg-zinc-800/50'
                    }`}
                  >
                    {navLinks[0].name}
                  </button>
                ) : (
                  <Link
                    to={navLinks[0].path}
                    onClick={() => setMobileMenuOpen(false)}
                    className={`block px-3 py-2 text-base font-medium rounded-lg transition-colors ${
                      isActivePath(navLinks[0].path)
                        ? 'text-amber-400 bg-amber-400/10'
                        : 'text-white hover:text-amber-400 hover:bg-zinc-800/50'
                    }`}
                  >
                    {navLinks[0].name}
                  </Link>
                )}

                {/* Unit Usaha Section - After Beranda */}
                <div className="py-2">
                  <h3 className="text-sm font-semibold text-zinc-300 mb-2 px-3">Unit Usaha</h3>
                  <div className="space-y-1 max-h-64 overflow-y-auto">
                    {unitUsaha.map((unit, index) => (
                      <Link
                        key={index}
                        to={unit.path}
                        onClick={() => setMobileMenuOpen(false)}
                        className={`flex items-center p-3 rounded-lg transition-all duration-200 ${
                          location.pathname === unit.path
                            ? 'bg-amber-600/20 border-l-4 border-amber-600'
                            : 'hover:bg-zinc-800/50'
                        }`}
                      >
                        <div
                          className={`w-10 h-10 rounded-lg overflow-hidden mr-3 transition-all duration-200 ${
                            location.pathname === unit.path ? 'ring-2 ring-amber-600' : ''
                          }`}
                        >
                          <img
                            src={unit.logo}
                            alt={`${unit.name} Logo`}
                            className="w-full h-full object-cover"
                            loading="lazy"
                          />
                        </div>
                        <div className="flex-1 min-w-0">
                          <h4
                            className={`font-medium text-sm transition-colors ${
                              location.pathname === unit.path
                                ? 'text-amber-400'
                                : 'text-white hover:text-amber-400'
                            }`}
                          >
                            {unit.name}
                          </h4>
                          <p className="text-zinc-400 text-xs line-clamp-1">{unit.subtitle}</p>
                        </div>
                      </Link>
                    ))}
                  </div>
                </div>

                {/* Remaining Navigation Links with dynamic content */}
                {navLinks.slice(1).map((link) => {
                  // Make navigation dynamic based on current unit
                  let linkPath = link.path
                  let linkText = link.name

                  if (link.name === 'Tentang Kami') {
                    linkPath = contentConfig.aboutPath
                    linkText = contentConfig.aboutText
                  } else if (link.name === 'Berita') {
                    linkPath = contentConfig.newsPath
                    linkText = contentConfig.newsText
                  } else if (link.name === 'Kontak') {
                    linkPath = contentConfig.contactPath
                  }

                  const isActive =
                    isActivePath(linkPath) ||
                    (link.name === 'Tentang Kami' &&
                      contentConfig.showUnitContent &&
                      isActivePath(currentUnit?.path))

                  return contentConfig.isScrollNavigation && linkPath.startsWith('#') ? (
                    <button
                      key={link.path}
                      onClick={(e) => handleNavClick(e, linkPath, linkText, link.name)}
                      className={`block w-full text-left px-3 py-2 text-base font-medium rounded-lg transition-colors ${
                        isActive
                          ? 'text-amber-400 bg-amber-400/10'
                          : 'text-white hover:text-amber-400 hover:bg-zinc-800/50'
                      }`}
                    >
                      {linkText}
                    </button>
                  ) : (
                    <Link
                      key={link.path}
                      to={linkPath}
                      onClick={() => setMobileMenuOpen(false)}
                      className={`block px-3 py-2 text-base font-medium rounded-lg transition-colors ${
                        isActive
                          ? 'text-amber-400 bg-amber-400/10'
                          : 'text-white hover:text-amber-400 hover:bg-zinc-800/50'
                      }`}
                    >
                      {linkText}
                    </Link>
                  )
                })}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile menu overlay */}
      {mobileMenuOpen && (
        <div
          className="fixed inset-0 z-40 bg-black/60 backdrop-blur-sm md:hidden animate-fade-in-scale"
          onClick={() => setMobileMenuOpen(false)}
        ></div>
      )}
    </>
  )
}

export default Navbar
