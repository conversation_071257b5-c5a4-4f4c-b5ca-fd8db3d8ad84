<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BusinessUnitGallery extends Model
{
    use HasFactory;

    protected $fillable = [
        'business_unit_id',
        'title',
        'description',
        'images',
        'type',
        'is_featured',
        'order',
    ];

    protected function casts(): array
    {
        return [
            'images' => 'array',
            'is_featured' => 'boolean',
            'order' => 'integer',
        ];
    }

    public function businessUnit(): BelongsTo
    {
        return $this->belongsTo(BusinessUnit::class);
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    public function scopeByType($query, string $type)
    {
        return $query->where('type', $type);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('order');
    }

    public function getFirstImage(): ?array
    {
        return $this->images[0] ?? null;
    }

    public function getImageCount(): int
    {
        return count($this->images ?? []);
    }
}
