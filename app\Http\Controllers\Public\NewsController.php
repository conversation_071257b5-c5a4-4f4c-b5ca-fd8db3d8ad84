<?php

namespace App\Http\Controllers\Public;

use App\Http\Controllers\Controller;
use App\Models\NewsArticle;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class NewsController extends Controller
{
    public function index(Request $request): Response
    {
        $query = NewsArticle::where('status', 'published')
            ->with('author')
            ->latest('published_at');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                    ->orWhere('excerpt', 'like', "%{$search}%")
                    ->orWhere('content', 'like', "%{$search}%");
            });
        }

        $articles = $query->paginate(12)->withQueryString();

        return Inertia::render('public/news/index', [
            'articles' => $articles,
            'search' => $request->get('search', ''),
        ]);
    }

    public function show(NewsArticle $newsArticle): Response
    {
        // Only show published articles
        if ($newsArticle->status !== 'published') {
            abort(404);
        }

        $newsArticle->load('author');

        // Get related articles (same category or recent)
        $relatedArticles = NewsArticle::where('status', 'published')
            ->where('id', '!=', $newsArticle->id)
            ->with('author')
            ->latest('published_at')
            ->limit(4)
            ->get();

        return Inertia::render('public/news/show', [
            'article' => [
                'id' => $newsArticle->id,
                'title' => $newsArticle->title,
                'slug' => $newsArticle->slug,
                'content' => $newsArticle->content,
                'excerpt' => $newsArticle->excerpt,
                'featured_image' => $newsArticle->featured_image,
                'published_at' => $newsArticle->published_at,
                'created_at' => $newsArticle->created_at,
                'author' => [
                    'name' => $newsArticle->author->name,
                ],
                'seo_data' => $newsArticle->seo_data,
            ],
            'related_articles' => $relatedArticles->map(function ($article) {
                return [
                    'id' => $article->id,
                    'title' => $article->title,
                    'slug' => $article->slug,
                    'excerpt' => $article->excerpt,
                    'featured_image' => $article->featured_image,
                    'published_at' => $article->published_at,
                    'author' => [
                        'name' => $article->author->name,
                    ],
                ];
            }),
        ]);
    }
}
