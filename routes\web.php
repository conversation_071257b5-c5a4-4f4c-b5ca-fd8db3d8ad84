<?php

use App\Http\Controllers\Public\BusinessUnitController;
use App\Http\Controllers\Public\HomeController;
use App\Http\Controllers\Public\NewsController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

// Public homepage
Route::get('/', [HomeController::class, 'index'])->name('home');

// Global contact routes
Route::get('/contact', [HomeController::class, 'contact'])->name('contact');
Route::post('/contact', [HomeController::class, 'submitContact'])->name('contact.submit');

// Public news routes
Route::prefix('news')->name('news.')->group(function () {
    Route::get('/', [NewsController::class, 'index'])->name('index');
    Route::get('/{newsArticle:slug}', [NewsController::class, 'show'])->name('show');
});

// Include auth routes BEFORE dynamic business unit routes to prevent conflicts
require __DIR__.'/auth.php';
require __DIR__.'/settings.php';
require __DIR__.'/admin.php';

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', function () {
        return Inertia::render('dashboard');
    })->name('dashboard');
});

// Dynamic business unit routes (MUST be last to avoid conflicts with other routes)
Route::prefix('{businessUnit:slug}')->group(function () {
    Route::get('/', [BusinessUnitController::class, 'index'])->name('business-unit.index');
    Route::get('/about', [BusinessUnitController::class, 'about'])->name('business-unit.about');
    Route::get('/contact', [BusinessUnitController::class, 'contact'])->name('business-unit.contact');
    Route::post('/contact', [BusinessUnitController::class, 'submitContact'])->name('business-unit.contact.submit');
    Route::get('/gallery', [BusinessUnitController::class, 'gallery'])->name('business-unit.gallery');

    // Specialized pages for specific business units
    Route::get('/pricing', [BusinessUnitController::class, 'pricing'])->name('business-unit.pricing');
    Route::get('/products', [BusinessUnitController::class, 'products'])->name('business-unit.products');
    Route::get('/menu', [BusinessUnitController::class, 'menu'])->name('business-unit.menu');
});
