import InputError from '@/components/input-error';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Head, Link, useForm } from '@inertiajs/react';
import { ArrowLeft, Building2, Mail, MapPin, Phone, Send } from 'lucide-react';

interface BusinessUnit {
    id: number;
    name: string;
    slug: string;
    description: string;
    status: string;
    primary_color?: string;
    contact_info?: {
        phone?: string;
        email?: string;
        address?: string;
    };
    social_media?: {
        instagram?: string;
        facebook?: string;
    };
}

interface BusinessUnitPage {
    id: number;
    type: string;
    title: string;
    slug: string;
    content: string;
    meta_data?: Record<string, unknown>;
    is_active: boolean;
    order: number;
}

interface Props {
    business_unit: BusinessUnit;
    page: BusinessUnitPage;
    pages: BusinessUnitPage[];
}

export default function BusinessUnitContact({ business_unit, page, pages }: Props) {
    const { data, setData, post, processing, errors, reset } = useForm({
        name: '',
        email: '',
        phone: '',
        subject: '',
        message: '',
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post(route('business-unit.contact.submit', business_unit.slug), {
            onSuccess: () => {
                reset();
            },
        });
    };

    const primaryColor = business_unit.primary_color || '#4285F4';

    return (
        <>
            <Head title={`Kontak - ${business_unit.name}`}>
                <meta name="description" content={`Hubungi ${business_unit.name} untuk informasi lebih lanjut tentang layanan kami`} />
            </Head>

            <div className="min-h-screen bg-gray-50">
                {/* Header */}
                <header className="bg-white shadow-sm">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="flex items-center justify-between py-6">
                            <div className="flex items-center">
                                <Link href="/" className="flex items-center">
                                    <Building2 className="mr-2 h-6 w-6 text-blue-600" />
                                    <span className="text-lg font-semibold text-gray-900">Cigi Global</span>
                                </Link>
                                <span className="mx-3 text-gray-400">/</span>
                                <Link href={`/${business_unit.slug}`} className="text-gray-700 hover:text-blue-600">
                                    {business_unit.name}
                                </Link>
                                <span className="mx-3 text-gray-400">/</span>
                                <span className="text-lg text-gray-600">Kontak</span>
                            </div>
                            <nav className="hidden space-x-6 md:flex">
                                <Link href="/" className="text-gray-700 hover:text-blue-600">
                                    Beranda
                                </Link>
                                <Link href={`/${business_unit.slug}`} className="text-gray-700 hover:text-blue-600">
                                    {business_unit.name}
                                </Link>
                                {pages.find((p) => p.type === 'about') && (
                                    <Link href={`/${business_unit.slug}/about`} className="text-gray-700 hover:text-blue-600">
                                        Tentang
                                    </Link>
                                )}
                            </nav>
                        </div>
                    </div>
                </header>

                {/* Hero Section */}
                <section className="py-16" style={{ backgroundColor: primaryColor }}>
                    <div className="mx-auto max-w-7xl px-4 text-center text-white sm:px-6 lg:px-8">
                        <Link href={`/${business_unit.slug}`} className="mb-4 inline-flex items-center text-white/80 hover:text-white">
                            <ArrowLeft className="mr-2 h-4 w-4" />
                            Kembali ke {business_unit.name}
                        </Link>
                        <h1 className="mb-4 text-4xl font-bold md:text-5xl">{page.title}</h1>
                        <p className="mx-auto max-w-3xl text-xl opacity-90">
                            Hubungi kami untuk informasi lebih lanjut tentang layanan {business_unit.name}
                        </p>
                    </div>
                </section>

                {/* Contact Content */}
                <section className="py-16">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="grid gap-8 lg:grid-cols-2">
                            {/* Contact Form */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Send className="h-5 w-5" />
                                        Kirim Pesan
                                    </CardTitle>
                                    <CardDescription>Isi formulir di bawah ini dan kami akan segera menghubungi Anda</CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <form onSubmit={handleSubmit} className="space-y-6">
                                        <div className="grid gap-4 md:grid-cols-2">
                                            <div className="space-y-2">
                                                <Label htmlFor="name">Nama Lengkap *</Label>
                                                <Input
                                                    id="name"
                                                    type="text"
                                                    value={data.name}
                                                    onChange={(e) => setData('name', e.target.value)}
                                                    placeholder="Masukkan nama lengkap"
                                                    required
                                                />
                                                <InputError message={errors.name} />
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="email">Email *</Label>
                                                <Input
                                                    id="email"
                                                    type="email"
                                                    value={data.email}
                                                    onChange={(e) => setData('email', e.target.value)}
                                                    placeholder="Masukkan alamat email"
                                                    required
                                                />
                                                <InputError message={errors.email} />
                                            </div>
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="phone">Nomor Telepon</Label>
                                            <Input
                                                id="phone"
                                                type="tel"
                                                value={data.phone}
                                                onChange={(e) => setData('phone', e.target.value)}
                                                placeholder="Masukkan nomor telepon (opsional)"
                                            />
                                            <InputError message={errors.phone} />
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="subject">Subjek *</Label>
                                            <Input
                                                id="subject"
                                                type="text"
                                                value={data.subject}
                                                onChange={(e) => setData('subject', e.target.value)}
                                                placeholder="Masukkan subjek pesan"
                                                required
                                            />
                                            <InputError message={errors.subject} />
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="message">Pesan *</Label>
                                            <Textarea
                                                id="message"
                                                value={data.message}
                                                onChange={(e) => setData('message', e.target.value)}
                                                placeholder="Tulis pesan Anda di sini..."
                                                rows={6}
                                                required
                                            />
                                            <InputError message={errors.message} />
                                        </div>

                                        <Button type="submit" disabled={processing} className="w-full" style={{ backgroundColor: primaryColor }}>
                                            <Send className="mr-2 h-4 w-4" />
                                            {processing ? 'Mengirim...' : 'Kirim Pesan'}
                                        </Button>
                                    </form>
                                </CardContent>
                            </Card>

                            {/* Contact Information */}
                            <div className="space-y-6">
                                {/* Contact Details */}
                                {business_unit.contact_info && (
                                    <Card>
                                        <CardHeader>
                                            <CardTitle>Informasi Kontak</CardTitle>
                                            <CardDescription>Hubungi kami melalui informasi di bawah ini</CardDescription>
                                        </CardHeader>
                                        <CardContent className="space-y-4">
                                            {business_unit.contact_info.phone && (
                                                <div className="flex items-center gap-3">
                                                    <Phone className="h-5 w-5 text-muted-foreground" />
                                                    <div>
                                                        <p className="font-medium">Telepon</p>
                                                        <a
                                                            href={`tel:${business_unit.contact_info.phone}`}
                                                            className="text-sm hover:underline"
                                                            style={{ color: primaryColor }}
                                                        >
                                                            {business_unit.contact_info.phone}
                                                        </a>
                                                    </div>
                                                </div>
                                            )}

                                            {business_unit.contact_info.email && (
                                                <div className="flex items-center gap-3">
                                                    <Mail className="h-5 w-5 text-muted-foreground" />
                                                    <div>
                                                        <p className="font-medium">Email</p>
                                                        <a
                                                            href={`mailto:${business_unit.contact_info.email}`}
                                                            className="text-sm hover:underline"
                                                            style={{ color: primaryColor }}
                                                        >
                                                            {business_unit.contact_info.email}
                                                        </a>
                                                    </div>
                                                </div>
                                            )}

                                            {business_unit.contact_info.address && (
                                                <div className="flex items-start gap-3">
                                                    <MapPin className="mt-0.5 h-5 w-5 text-muted-foreground" />
                                                    <div>
                                                        <p className="font-medium">Alamat</p>
                                                        <p className="text-sm text-muted-foreground">{business_unit.contact_info.address}</p>
                                                    </div>
                                                </div>
                                            )}
                                        </CardContent>
                                    </Card>
                                )}

                                {/* Additional Content */}
                                {page.content && (
                                    <Card>
                                        <CardContent className="pt-6">
                                            <div dangerouslySetInnerHTML={{ __html: page.content }} className="prose prose-sm max-w-none" />
                                        </CardContent>
                                    </Card>
                                )}
                            </div>
                        </div>
                    </div>
                </section>

                {/* Footer */}
                <footer className="bg-gray-900 py-12 text-white">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="text-center">
                            <div className="mb-4 flex items-center justify-center">
                                <Building2 className="mr-2 h-6 w-6" />
                                <h3 className="text-xl font-bold">{business_unit.name}</h3>
                            </div>
                            <p className="mx-auto mb-6 max-w-2xl text-gray-400">{business_unit.description}</p>
                            <div className="flex justify-center space-x-6">
                                <Link href="/" className="text-gray-400 hover:text-white">
                                    Kembali ke Cigi Global
                                </Link>
                                <Link href={`/${business_unit.slug}`} className="text-gray-400 hover:text-white">
                                    {business_unit.name}
                                </Link>
                            </div>
                        </div>
                    </div>
                </footer>
            </div>
        </>
    );
}
