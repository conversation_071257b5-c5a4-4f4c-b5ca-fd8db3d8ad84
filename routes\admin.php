<?php

use App\Http\Controllers\Admin\BusinessUnitController;
use App\Http\Controllers\Admin\BusinessUnitPageController;
use App\Http\Controllers\Admin\ContactSubmissionController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\GalleryController;
use App\Http\Controllers\Admin\GlobalSettingController;
use App\Http\Controllers\Admin\NewsController;
use App\Http\Controllers\Admin\UserController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Admin Routes
|--------------------------------------------------------------------------
|
| Here is where you can register admin routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group and require authentication.
|
*/

Route::middleware(['auth', 'role:admin|super-admin'])->prefix('admin')->name('admin.')->group(function () {
    // Dashboard
    Route::get('/', DashboardController::class)->name('dashboard');
    Route::get('/dashboard', DashboardController::class)->name('dashboard.index');

    // Business Units Management
    Route::resource('business-units', BusinessUnitController::class)->parameters([
        'business-units' => 'business_unit',
    ]);

    // Business Unit Pages Management
    Route::prefix('business-units/{businessUnit}/pages')->name('business-units.pages.')->group(function () {
        Route::get('/', [BusinessUnitPageController::class, 'index'])->name('index');
        Route::get('/create', [BusinessUnitPageController::class, 'create'])->name('create');
        Route::post('/', [BusinessUnitPageController::class, 'store'])->name('store');
        Route::get('/{page}', [BusinessUnitPageController::class, 'show'])->name('show');
        Route::get('/{page}/edit', [BusinessUnitPageController::class, 'edit'])->name('edit');
        Route::put('/{page}', [BusinessUnitPageController::class, 'update'])->name('update');
        Route::delete('/{page}', [BusinessUnitPageController::class, 'destroy'])->name('destroy');
        Route::post('/{page}/duplicate', [BusinessUnitPageController::class, 'duplicate'])->name('duplicate');
    });

    // Gallery Management
    Route::prefix('business-units/{businessUnit}/gallery')->name('business-units.gallery.')->group(function () {
        Route::get('/', [GalleryController::class, 'index'])->name('index');
        Route::post('/upload', [GalleryController::class, 'upload'])->name('upload');
        Route::delete('/{media}', [GalleryController::class, 'destroy'])->name('destroy');
        Route::post('/bulk-delete', [GalleryController::class, 'bulkDelete'])->name('bulk-delete');
        Route::post('/reorder', [GalleryController::class, 'reorder'])->name('reorder');
    });

    // News Management
    Route::resource('news', NewsController::class);

    // Contact Submissions Management
    Route::prefix('contact-submissions')->name('contact-submissions.')->group(function () {
        Route::get('/', [ContactSubmissionController::class, 'index'])->name('index');
        Route::get('/{contactSubmission}', [ContactSubmissionController::class, 'show'])->name('show');
        Route::post('/{contactSubmission}/mark-replied', [ContactSubmissionController::class, 'markAsReplied'])->name('mark-replied');
    });

    // Global Settings Management
    Route::prefix('settings')->name('settings.')->group(function () {
        Route::get('/', [GlobalSettingController::class, 'index'])->name('index');
        Route::put('/', [GlobalSettingController::class, 'update'])->name('update');
        Route::post('/reset', [GlobalSettingController::class, 'reset'])->name('reset');
    });

    // User Management (Super Admin only)
    Route::middleware('role:super-admin')->group(function () {
        Route::resource('users', UserController::class);
    });
});
