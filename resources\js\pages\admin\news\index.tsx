import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, router } from '@inertiajs/react';
import { Edit, Eye, Plus, Search, Trash2 } from 'lucide-react';
import { useState } from 'react';

interface NewsArticle {
    id: number;
    title: string;
    slug: string;
    excerpt: string;
    status: string;
    published_at: string | null;
    created_at: string;
    author: {
        name: string;
    };
}

interface PaginatedArticles {
    data: NewsArticle[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    links: Array<{
        url: string | null;
        label: string;
        active: boolean;
    }>;
}

interface Props {
    articles: PaginatedArticles;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Admin',
        href: '/admin',
    },
    {
        title: 'Berita',
        href: '/admin/news',
    },
];

export default function NewsIndex({ articles }: Props) {
    const [searchTerm, setSearchTerm] = useState('');

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();
        router.get(route('admin.news.index'), { search: searchTerm }, { preserveState: true });
    };

    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'published':
                return <Badge className="bg-green-100 text-green-800">Dipublikasi</Badge>;
            case 'draft':
                return <Badge className="bg-yellow-100 text-yellow-800">Draft</Badge>;
            default:
                return <Badge>{status}</Badge>;
        }
    };

    const handleDelete = (article: NewsArticle) => {
        if (confirm(`Apakah Anda yakin ingin menghapus artikel "${article.title}"?`)) {
            router.delete(route('admin.news.destroy', article.id));
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Kelola Berita" />

            <div className="flex h-full flex-1 flex-col gap-4 p-4">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold tracking-tight">Kelola Berita</h1>
                        <p className="text-muted-foreground">Kelola artikel berita dan aktivitas Cigi Global</p>
                    </div>
                    <Link href="/admin/news/create">
                        <Button>
                            <Plus className="mr-2 h-4 w-4" />
                            Tambah Artikel
                        </Button>
                    </Link>
                </div>

                {/* Search */}
                <Card>
                    <CardContent className="pt-6">
                        <form onSubmit={handleSearch} className="flex gap-2">
                            <Input
                                placeholder="Cari artikel berita..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                className="flex-1"
                            />
                            <Button type="submit" variant="outline">
                                <Search className="h-4 w-4" />
                            </Button>
                        </form>
                    </CardContent>
                </Card>

                {/* Articles List */}
                <Card>
                    <CardHeader>
                        <CardTitle>Artikel Berita</CardTitle>
                        <CardDescription>
                            Menampilkan {articles.data.length} dari {articles.total} artikel
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        {articles.data.length > 0 ? (
                            <div className="space-y-4">
                                {articles.data.map((article) => (
                                    <div key={article.id} className="flex items-start justify-between rounded-lg border p-4">
                                        <div className="min-w-0 flex-1">
                                            <div className="mb-2 flex items-center gap-2">
                                                <h3 className="truncate text-lg font-medium">{article.title}</h3>
                                                {getStatusBadge(article.status)}
                                            </div>

                                            {article.excerpt && <p className="mb-2 line-clamp-2 text-sm text-muted-foreground">{article.excerpt}</p>}

                                            <div className="flex items-center gap-4 text-xs text-muted-foreground">
                                                <span>oleh {article.author.name}</span>
                                                <span>•</span>
                                                <span>
                                                    {article.status === 'published' && article.published_at
                                                        ? `Dipublikasi ${new Date(article.published_at).toLocaleDateString('id-ID')}`
                                                        : `Dibuat ${new Date(article.created_at).toLocaleDateString('id-ID')}`}
                                                </span>
                                                <span>•</span>
                                                <span>/{article.slug}</span>
                                            </div>
                                        </div>

                                        <div className="ml-4 flex gap-2">
                                            <Link href={`/admin/news/${article.id}`}>
                                                <Button variant="outline" size="sm">
                                                    <Eye className="h-3 w-3" />
                                                </Button>
                                            </Link>
                                            <Link href={`/admin/news/${article.id}/edit`}>
                                                <Button variant="outline" size="sm">
                                                    <Edit className="h-3 w-3" />
                                                </Button>
                                            </Link>
                                            {article.status === 'published' && (
                                                <Link href={`/news/${article.slug}`} target="_blank">
                                                    <Button variant="outline" size="sm">
                                                        <Eye className="h-3 w-3" />
                                                    </Button>
                                                </Link>
                                            )}
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => handleDelete(article)}
                                                className="text-red-600 hover:text-red-700"
                                            >
                                                <Trash2 className="h-3 w-3" />
                                            </Button>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        ) : (
                            <div className="py-12 text-center">
                                <h3 className="mb-2 text-lg font-medium">Belum ada artikel berita</h3>
                                <p className="mb-4 text-muted-foreground">Mulai dengan membuat artikel berita pertama</p>
                                <Link href="/admin/news/create">
                                    <Button>
                                        <Plus className="mr-2 h-4 w-4" />
                                        Buat Artikel Pertama
                                    </Button>
                                </Link>
                            </div>
                        )}
                    </CardContent>
                </Card>

                {/* Pagination */}
                {articles.last_page > 1 && (
                    <Card>
                        <CardContent className="pt-6">
                            <div className="flex items-center justify-between">
                                <p className="text-sm text-muted-foreground">
                                    Halaman {articles.current_page} dari {articles.last_page}
                                </p>
                                <div className="flex gap-2">
                                    {articles.links.map((link, index) => (
                                        <Button
                                            key={index}
                                            variant={link.active ? 'default' : 'outline'}
                                            size="sm"
                                            disabled={!link.url}
                                            onClick={() => link.url && router.get(link.url)}
                                            dangerouslySetInnerHTML={{ __html: link.label }}
                                        />
                                    ))}
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                )}
            </div>
        </AppLayout>
    );
}
