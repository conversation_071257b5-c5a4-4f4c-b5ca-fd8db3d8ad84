<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\BusinessUnit;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class GalleryController extends Controller
{
    use AuthorizesRequests;

    public function index(BusinessUnit $businessUnit): Response
    {
        $this->authorize('view galleries');

        $businessUnit->load('media');

        // Get gallery images
        $galleryImages = $businessUnit->getMedia('gallery')->map(function ($media) {
            return [
                'id' => $media->id,
                'name' => $media->name,
                'file_name' => $media->file_name,
                'mime_type' => $media->mime_type,
                'size' => $media->size,
                'url' => $media->getUrl(),
                'thumb_url' => $media->getUrl('thumb'),
                'preview_url' => $media->getUrl('preview'),
                'created_at' => $media->created_at,
            ];
        });

        return Inertia::render('admin/galleries/index', [
            'business_unit' => [
                'id' => $businessUnit->id,
                'name' => $businessUnit->name,
                'slug' => $businessUnit->slug,
            ],
            'gallery_images' => $galleryImages,
        ]);
    }

    public function upload(Request $request, BusinessUnit $businessUnit): JsonResponse
    {
        $this->authorize('upload galleries');

        $request->validate([
            'files' => 'required|array',
            'files.*' => 'required|image|mimes:jpeg,png,jpg,gif,webp|max:10240', // 10MB max
        ]);

        $uploadedFiles = [];

        foreach ($request->file('files') as $file) {
            $media = $businessUnit
                ->addMediaFromRequest('files')
                ->usingFileName($file->getClientOriginalName())
                ->toMediaCollection('gallery');

            $uploadedFiles[] = [
                'id' => $media->id,
                'name' => $media->name,
                'file_name' => $media->file_name,
                'url' => $media->getUrl(),
                'thumb_url' => $media->getUrl('thumb'),
                'preview_url' => $media->getUrl('preview'),
            ];
        }

        return response()->json([
            'message' => 'Gambar berhasil diunggah.',
            'files' => $uploadedFiles,
        ]);
    }

    public function destroy(BusinessUnit $businessUnit, Media $media): RedirectResponse
    {
        $this->authorize('delete galleries');

        // Ensure the media belongs to this business unit
        if ($media->model_id !== $businessUnit->id || $media->model_type !== BusinessUnit::class) {
            abort(403, 'Unauthorized action.');
        }

        $media->delete();

        return back()->with('success', 'Gambar berhasil dihapus.');
    }

    public function bulkDelete(Request $request, BusinessUnit $businessUnit): RedirectResponse
    {
        $this->authorize('delete galleries');

        $request->validate([
            'media_ids' => 'required|array',
            'media_ids.*' => 'required|integer|exists:media,id',
        ]);

        $mediaItems = Media::whereIn('id', $request->media_ids)
            ->where('model_id', $businessUnit->id)
            ->where('model_type', BusinessUnit::class)
            ->get();

        foreach ($mediaItems as $media) {
            $media->delete();
        }

        return back()->with('success', count($mediaItems).' gambar berhasil dihapus.');
    }

    public function reorder(Request $request, BusinessUnit $businessUnit): JsonResponse
    {
        $this->authorize('edit galleries');

        $request->validate([
            'media_ids' => 'required|array',
            'media_ids.*' => 'required|integer|exists:media,id',
        ]);

        foreach ($request->media_ids as $index => $mediaId) {
            Media::where('id', $mediaId)
                ->where('model_id', $businessUnit->id)
                ->where('model_type', BusinessUnit::class)
                ->update(['order_column' => $index + 1]);
        }

        return response()->json(['message' => 'Urutan gambar berhasil diperbarui.']);
    }
}
