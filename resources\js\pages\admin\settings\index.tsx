import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, useForm } from '@inertiajs/react';
import { RefreshCw, Save } from 'lucide-react';

interface GlobalSetting {
    id: number;
    key: string;
    value: string;
    type: string;
    group: string;
    description: string;
}

interface Props {
    settings: Record<string, GlobalSetting[]>;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Admin',
        href: '/admin',
    },
    {
        title: 'Pengaturan Global',
        href: '/admin/settings',
    },
];

export default function GlobalSettings({ settings }: Props) {
    // Convert settings to a flat object for the form
    const initialSettings: Record<string, string> = {};
    Object.values(settings)
        .flat()
        .forEach((setting) => {
            initialSettings[setting.key] = setting.value;
        });

    const { data, setData, put, processing, errors } = useForm({
        settings: initialSettings,
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        put(route('admin.settings.update'));
    };

    const handleReset = () => {
        if (confirm('Apakah Anda yakin ingin mereset semua pengaturan ke nilai default?')) {
            // This would call the reset endpoint
            window.location.href = route('admin.settings.reset');
        }
    };

    const updateSetting = (key: string, value: string | boolean) => {
        setData('settings', {
            ...data.settings,
            [key]: value.toString(),
        });
    };

    const renderField = (setting: GlobalSetting) => {
        const value = data.settings[setting.key] || '';
        const error = errors[`settings.${setting.key}`];

        switch (setting.type) {
            case 'textarea':
                return (
                    <div className="space-y-2">
                        <Label htmlFor={setting.key}>{setting.key.replace(/_/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase())}</Label>
                        <Textarea
                            id={setting.key}
                            value={value}
                            onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => updateSetting(setting.key, e.target.value)}
                            placeholder={setting.description}
                            rows={3}
                            className={error ? 'border-red-500' : ''}
                        />
                        {setting.description && <p className="text-xs text-muted-foreground">{setting.description}</p>}
                        {error && <p className="text-sm text-red-500">{error}</p>}
                    </div>
                );

            case 'boolean':
                return (
                    <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                            <Checkbox
                                id={setting.key}
                                checked={value === 'true'}
                                onCheckedChange={(checked) => updateSetting(setting.key, checked)}
                            />
                            <Label htmlFor={setting.key}>{setting.key.replace(/_/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase())}</Label>
                        </div>
                        {setting.description && <p className="text-xs text-muted-foreground">{setting.description}</p>}
                        {error && <p className="text-sm text-red-500">{error}</p>}
                    </div>
                );

            default:
                return (
                    <div className="space-y-2">
                        <Label htmlFor={setting.key}>{setting.key.replace(/_/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase())}</Label>
                        <Input
                            id={setting.key}
                            type={setting.type}
                            value={value}
                            onChange={(e) => updateSetting(setting.key, e.target.value)}
                            placeholder={setting.description}
                            className={error ? 'border-red-500' : ''}
                        />
                        {setting.description && <p className="text-xs text-muted-foreground">{setting.description}</p>}
                        {error && <p className="text-sm text-red-500">{error}</p>}
                    </div>
                );
        }
    };

    const getGroupTitle = (group: string) => {
        const titles: Record<string, string> = {
            site: 'Informasi Website',
            homepage: 'Halaman Beranda',
            contact: 'Informasi Kontak',
            social: 'Media Sosial',
            seo: 'SEO & Analytics',
            email: 'Pengaturan Email',
            maintenance: 'Mode Pemeliharaan',
            general: 'Umum',
        };
        return titles[group] || group;
    };

    const getGroupDescription = (group: string) => {
        const descriptions: Record<string, string> = {
            site: 'Pengaturan dasar website seperti judul dan deskripsi',
            homepage: 'Konten yang ditampilkan di halaman beranda',
            contact: 'Informasi kontak yang ditampilkan di website',
            social: 'Akun media sosial Cigi Global',
            seo: 'Pengaturan untuk optimasi mesin pencari dan analytics',
            email: 'Konfigurasi email untuk notifikasi dan form kontak',
            maintenance: 'Pengaturan mode pemeliharaan website',
            general: 'Pengaturan umum lainnya',
        };
        return descriptions[group] || '';
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Pengaturan Global" />

            <div className="flex h-full flex-1 flex-col gap-4 p-4">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold tracking-tight">Pengaturan Global</h1>
                        <p className="text-muted-foreground">Kelola pengaturan website secara keseluruhan</p>
                    </div>
                    <div className="flex gap-2">
                        <Button variant="outline" onClick={handleReset}>
                            <RefreshCw className="mr-2 h-4 w-4" />
                            Reset ke Default
                        </Button>
                    </div>
                </div>

                {/* Settings Form */}
                <form onSubmit={handleSubmit} className="space-y-6">
                    {Object.entries(settings).map(([group, groupSettings]) => (
                        <Card key={group}>
                            <CardHeader>
                                <CardTitle>{getGroupTitle(group)}</CardTitle>
                                <CardDescription>{getGroupDescription(group)}</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                {groupSettings.map((setting, index) => (
                                    <div key={setting.key}>
                                        {renderField(setting)}
                                        {index < groupSettings.length - 1 && <Separator className="mt-6" />}
                                    </div>
                                ))}
                            </CardContent>
                        </Card>
                    ))}

                    {/* Submit Button */}
                    <div className="flex justify-end">
                        <Button type="submit" disabled={processing}>
                            <Save className="mr-2 h-4 w-4" />
                            {processing ? 'Menyimpan...' : 'Simpan Pengaturan'}
                        </Button>
                    </div>
                </form>

                {/* Help Section */}
                <Card>
                    <CardHeader>
                        <CardTitle>Bantuan</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4 text-sm text-muted-foreground">
                            <div>
                                <h4 className="mb-2 font-medium text-foreground">Tips Penggunaan:</h4>
                                <ul className="list-inside list-disc space-y-1">
                                    <li>Pastikan untuk mengisi informasi kontak yang akurat</li>
                                    <li>Gunakan deskripsi yang menarik untuk SEO</li>
                                    <li>Upload gambar dengan ukuran yang sesuai untuk performa optimal</li>
                                    <li>Aktifkan mode pemeliharaan saat melakukan update besar</li>
                                </ul>
                            </div>
                            <div>
                                <h4 className="mb-2 font-medium text-foreground">Catatan Penting:</h4>
                                <ul className="list-inside list-disc space-y-1">
                                    <li>Perubahan akan langsung terlihat di website publik</li>
                                    <li>Backup pengaturan secara berkala</li>
                                    <li>Test semua perubahan sebelum mempublikasikan</li>
                                </ul>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
