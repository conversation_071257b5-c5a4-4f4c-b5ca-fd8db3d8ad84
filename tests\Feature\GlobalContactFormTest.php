<?php

use App\Models\ContactSubmission;
use App\Models\GlobalSetting;
use Illuminate\Support\Facades\Mail;

beforeEach(function () {
    // Run migrations
    $this->artisan('migrate:fresh');

    // Create global settings
    GlobalSetting::create([
        'key' => 'contact_email',
        'value' => '<EMAIL>',
    ]);

    GlobalSetting::create([
        'key' => 'contact_phone',
        'value' => '+62123456789',
    ]);

    GlobalSetting::create([
        'key' => 'contact_address',
        'value' => 'Jl. Cigi Global No. 123, Jakarta',
    ]);
});

it('can display global contact page', function () {
    $response = $this->get(route('contact'));

    $response->assertStatus(200);
    $response->assertInertia(fn ($page) => $page->component('public/contact'));
});

it('can submit global contact form', function () {
    Mail::fake();

    $contactData = [
        'name' => '<PERSON>',
        'email' => '<EMAIL>',
        'phone' => '+***********',
        'subject' => 'Pertanyaan tentang Cigi Global',
        'message' => 'Saya ingin mengetahui lebih lanjut tentang Cigi Global.',
    ];

    $response = $this->post(route('contact.submit'), $contactData);

    $response->assertRedirect();
    $response->assertSessionHas('success');

    // Check if global submission was stored (business_unit_id = null)
    $this->assertDatabaseHas('contact_submissions', [
        'business_unit_id' => null,
        'name' => 'John Doe',
        'email' => '<EMAIL>',
        'subject' => 'Pertanyaan tentang Cigi Global',
        'status' => 'new',
    ]);

    // Check if email was queued
    Mail::assertQueued(\App\Mail\ContactFormSubmitted::class);
});

it('validates global contact form data', function () {
    $response = $this->post(route('contact.submit'), [
        'name' => '',
        'email' => 'invalid-email',
        'subject' => '',
        'message' => '',
    ]);

    $response->assertSessionHasErrors(['name', 'email', 'subject', 'message']);
});

it('stores metadata with global contact submission', function () {
    Mail::fake();

    $contactData = [
        'name' => 'Jane Doe',
        'email' => '<EMAIL>',
        'subject' => 'Test Subject',
        'message' => 'Test message',
    ];

    $response = $this->post(route('contact.submit'), $contactData);

    $submission = ContactSubmission::first();

    expect($submission->metadata)->toHaveKey('ip');
    expect($submission->metadata)->toHaveKey('user_agent');
    expect($submission->metadata)->toHaveKey('submitted_at');
    expect($submission->metadata)->toHaveKey('type');
    expect($submission->metadata['type'])->toBe('global');
});

it('can identify global submissions', function () {
    $globalSubmission = ContactSubmission::factory()->create([
        'business_unit_id' => null,
    ]);

    expect($globalSubmission->isGlobal())->toBeTrue();
});

it('can scope global submissions', function () {
    // Create global submission
    ContactSubmission::factory()->create([
        'business_unit_id' => null,
    ]);

    // Create business unit submission
    ContactSubmission::factory()->create();

    $globalSubmissions = ContactSubmission::global()->get();
    $businessUnitSubmissions = ContactSubmission::businessUnit()->get();

    expect($globalSubmissions)->toHaveCount(1);
    expect($businessUnitSubmissions)->toHaveCount(1);
});

it('sends email to global contact email setting', function () {
    Mail::fake();

    $contactData = [
        'name' => 'Test User',
        'email' => '<EMAIL>',
        'subject' => 'Test Subject',
        'message' => 'Test message',
    ];

    $this->post(route('contact.submit'), $contactData);

    Mail::assertQueued(\App\Mail\ContactFormSubmitted::class, function ($mail) {
        return $mail->hasTo('<EMAIL>');
    });
});

it('handles global contact email template correctly', function () {
    $globalSubmission = ContactSubmission::factory()->create([
        'business_unit_id' => null,
    ]);

    $mail = new \App\Mail\ContactFormSubmitted($globalSubmission);
    $envelope = $mail->envelope();

    expect($envelope->subject)->toContain('Cigi Global');
});
