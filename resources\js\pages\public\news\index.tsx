import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Head, Link, router } from '@inertiajs/react';
import { Building2, Calendar, Search, User } from 'lucide-react';
import { useState } from 'react';

interface NewsArticle {
    id: number;
    title: string;
    slug: string;
    excerpt?: string;
    featured_image?: string;
    published_at: string;
    author: {
        name: string;
    };
}

interface PaginatedArticles {
    data: NewsArticle[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    links: Array<{
        url: string | null;
        label: string;
        active: boolean;
    }>;
}

interface Props {
    articles: PaginatedArticles;
    search: string;
}

export default function NewsIndex({ articles, search }: Props) {
    const [searchTerm, setSearchTerm] = useState(search);

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();
        router.get(route('news.index'), { search: searchTerm }, { preserveState: true });
    };

    return (
        <>
            <Head title="Berita - Cigi Global">
                <meta name="description" content="Berita terbaru dan aktivitas dari Cigi Global dan unit-unit bisnisnya" />
            </Head>

            <div className="min-h-screen bg-gray-50">
                {/* Header */}
                <header className="bg-white shadow-sm">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="flex items-center justify-between py-6">
                            <div className="flex items-center">
                                <Link href="/" className="flex items-center">
                                    <Building2 className="mr-2 h-6 w-6 text-blue-600" />
                                    <span className="text-lg font-semibold text-gray-900">Cigi Global</span>
                                </Link>
                                <span className="mx-3 text-gray-400">/</span>
                                <span className="text-lg text-gray-600">Berita</span>
                            </div>
                            <nav className="hidden space-x-6 md:flex">
                                <Link href="/" className="text-gray-700 hover:text-blue-600">
                                    Beranda
                                </Link>
                                <Link href="/news" className="font-medium text-blue-600">
                                    Berita
                                </Link>
                                <Link href="/login" className="text-gray-700 hover:text-blue-600">
                                    Login
                                </Link>
                            </nav>
                        </div>
                    </div>
                </header>

                {/* Hero Section */}
                <section className="bg-gradient-to-r from-blue-600 to-blue-800 py-16 text-white">
                    <div className="mx-auto max-w-7xl px-4 text-center sm:px-6 lg:px-8">
                        <h1 className="mb-4 text-4xl font-bold md:text-5xl">Berita Cigi Global</h1>
                        <p className="mx-auto max-w-3xl text-xl opacity-90">
                            Ikuti perkembangan terbaru dan aktivitas dari Cigi Global dan seluruh unit bisnisnya
                        </p>
                    </div>
                </section>

                {/* Search Section */}
                <section className="bg-white py-8">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <form onSubmit={handleSearch} className="mx-auto max-w-md">
                            <div className="flex gap-2">
                                <Input
                                    placeholder="Cari berita..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="flex-1"
                                />
                                <Button type="submit" variant="outline">
                                    <Search className="h-4 w-4" />
                                </Button>
                            </div>
                        </form>
                        {search && (
                            <div className="mt-4 text-center">
                                <p className="text-gray-600">
                                    Hasil pencarian untuk: <strong>"{search}"</strong>
                                </p>
                                <Link href="/news">
                                    <Button variant="link" size="sm">
                                        Hapus filter
                                    </Button>
                                </Link>
                            </div>
                        )}
                    </div>
                </section>

                {/* Articles Section */}
                <section className="py-16">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        {articles.data.length > 0 ? (
                            <>
                                <div className="mb-8">
                                    <h2 className="mb-2 text-2xl font-bold text-gray-900">
                                        {search ? `Hasil Pencarian (${articles.total})` : 'Berita Terbaru'}
                                    </h2>
                                    <p className="text-gray-600">
                                        Menampilkan {articles.data.length} dari {articles.total} artikel
                                    </p>
                                </div>

                                <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
                                    {articles.data.map((article) => (
                                        <Card key={article.id} className="transition-shadow hover:shadow-lg">
                                            {article.featured_image && (
                                                <div className="aspect-video overflow-hidden rounded-t-lg">
                                                    <img
                                                        src={article.featured_image}
                                                        alt={article.title}
                                                        className="h-full w-full object-cover transition-transform duration-300 hover:scale-105"
                                                    />
                                                </div>
                                            )}
                                            <CardHeader>
                                                <CardTitle className="line-clamp-2 text-lg hover:text-blue-600">
                                                    <Link href={`/news/${article.slug}`}>{article.title}</Link>
                                                </CardTitle>
                                                <div className="flex items-center gap-4 text-sm text-gray-600">
                                                    <div className="flex items-center gap-1">
                                                        <Calendar className="h-3 w-3" />
                                                        <span>
                                                            {new Date(article.published_at).toLocaleDateString('id-ID', {
                                                                year: 'numeric',
                                                                month: 'long',
                                                                day: 'numeric',
                                                            })}
                                                        </span>
                                                    </div>
                                                    <div className="flex items-center gap-1">
                                                        <User className="h-3 w-3" />
                                                        <span>{article.author.name}</span>
                                                    </div>
                                                </div>
                                            </CardHeader>
                                            <CardContent>
                                                {article.excerpt && (
                                                    <CardDescription className="mb-4 line-clamp-3">{article.excerpt}</CardDescription>
                                                )}
                                                <Link href={`/news/${article.slug}`}>
                                                    <Button variant="outline" size="sm">
                                                        Baca Selengkapnya
                                                    </Button>
                                                </Link>
                                            </CardContent>
                                        </Card>
                                    ))}
                                </div>

                                {/* Pagination */}
                                {articles.last_page > 1 && (
                                    <div className="mt-12 flex justify-center">
                                        <div className="flex items-center gap-2">
                                            {articles.links.map((link, index) => (
                                                <Button
                                                    key={index}
                                                    variant={link.active ? 'default' : 'outline'}
                                                    size="sm"
                                                    disabled={!link.url}
                                                    onClick={() => link.url && router.get(link.url)}
                                                    dangerouslySetInnerHTML={{ __html: link.label }}
                                                />
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </>
                        ) : (
                            <div className="py-12 text-center">
                                <h3 className="mb-2 text-xl font-medium text-gray-900">
                                    {search ? 'Tidak ada hasil pencarian' : 'Belum ada berita'}
                                </h3>
                                <p className="mb-6 text-gray-600">
                                    {search ? `Tidak ditemukan berita yang sesuai dengan "${search}"` : 'Berita akan segera ditambahkan'}
                                </p>
                                {search && (
                                    <Link href="/news">
                                        <Button>Lihat Semua Berita</Button>
                                    </Link>
                                )}
                            </div>
                        )}
                    </div>
                </section>

                {/* Footer */}
                <footer className="bg-gray-900 py-12 text-white">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="text-center">
                            <div className="mb-4 flex items-center justify-center">
                                <Building2 className="mr-2 h-6 w-6" />
                                <h3 className="text-xl font-bold">Cigi Global</h3>
                            </div>
                            <p className="mx-auto mb-6 max-w-2xl text-gray-400">
                                Pusat bisnis terpadu yang mengembangkan berbagai unit usaha untuk kemajuan masyarakat desa.
                            </p>
                            <div className="flex justify-center space-x-6">
                                <Link href="/" className="text-gray-400 hover:text-white">
                                    Beranda
                                </Link>
                                <Link href="/news" className="text-gray-400 hover:text-white">
                                    Berita
                                </Link>
                                <Link href="/login" className="text-gray-400 hover:text-white">
                                    Login
                                </Link>
                            </div>
                        </div>
                    </div>
                </footer>
            </div>
        </>
    );
}
