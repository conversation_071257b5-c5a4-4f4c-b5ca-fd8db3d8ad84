<?php

namespace Database\Factories;

use App\Models\BusinessUnit;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ContactSubmission>
 */
class ContactSubmissionFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'business_unit_id' => BusinessUnit::factory(),
            'name' => fake()->name(),
            'email' => fake()->safeEmail(),
            'phone' => fake()->phoneNumber(),
            'subject' => fake()->sentence(),
            'message' => fake()->paragraph(),
            'status' => fake()->randomElement(['new', 'read', 'replied']),
            'metadata' => [
                'ip' => fake()->ipv4(),
                'user_agent' => fake()->userAgent(),
                'submitted_at' => now()->toISOString(),
            ],
        ];
    }

    /**
     * Indicate that the submission is new.
     */
    public function unread(): static
    {
        return $this->state(fn () => [
            'status' => 'new',
            'read_at' => null,
            'replied_at' => null,
        ]);
    }

    /**
     * Indicate that the submission has been read.
     */
    public function read(): static
    {
        return $this->state(fn () => [
            'status' => 'read',
            'read_at' => fake()->dateTimeBetween('-1 week', 'now'),
            'replied_at' => null,
        ]);
    }

    /**
     * Indicate that the submission has been replied to.
     */
    public function replied(): static
    {
        return $this->state(fn () => [
            'status' => 'replied',
            'read_at' => fake()->dateTimeBetween('-1 week', '-1 day'),
            'replied_at' => fake()->dateTimeBetween('-1 day', 'now'),
        ]);
    }
}
