import { NavFooter } from '@/components/nav-footer';
import { NavMain } from '@/components/nav-main';
import { NavUser } from '@/components/nav-user';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { type NavItem, type SharedData } from '@/types';
import { Link, usePage } from '@inertiajs/react';
import { BookOpen, Building2, FileText, Folder, LayoutGrid, Mail, Settings, Users } from 'lucide-react';
import AppLogo from './app-logo';

const mainNavItems: NavItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
        icon: LayoutGrid,
    },
];

const footerNavItems: NavItem[] = [
    {
        title: 'Repository',
        href: 'https://github.com/laravel/react-starter-kit',
        icon: Folder,
    },
    {
        title: 'Documentation',
        href: 'https://laravel.com/docs/starter-kits#react',
        icon: BookOpen,
    },
];

export function AppSidebar() {
    const { auth } = usePage<SharedData>().props;
    const userRoles = auth.roles || [];
    const isAdmin = userRoles.includes('admin') || userRoles.includes('super-admin');
    const isSuperAdmin = userRoles.includes('super-admin');

    // Admin navigation items
    const adminNavItems: NavItem[] = isAdmin
        ? [
              {
                  title: 'Dashboard Admin',
                  href: '/admin',
                  icon: LayoutGrid,
              },
              {
                  title: 'Unit Bisnis',
                  href: '/admin/business-units',
                  icon: Building2,
              },
              {
                  title: 'Berita',
                  href: '/admin/news',
                  icon: FileText,
              },
              {
                  title: 'Pesan Kontak',
                  href: '/admin/contact-submissions',
                  icon: Mail,
              },
              {
                  title: 'Pengaturan',
                  href: '/admin/settings',
                  icon: Settings,
              },
              ...(isSuperAdmin
                  ? [
                        {
                            title: 'Kelola Pengguna',
                            href: '/admin/users',
                            icon: Users,
                        },
                    ]
                  : []),
          ]
        : [];

    return (
        <Sidebar collapsible="icon" variant="inset">
            <SidebarHeader>
                <SidebarMenu>
                    <SidebarMenuItem>
                        <SidebarMenuButton size="lg" asChild>
                            <Link href={isAdmin ? '/admin' : '/dashboard'} prefetch>
                                <AppLogo />
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarHeader>

            <SidebarContent>{isAdmin ? <NavMain items={adminNavItems} /> : <NavMain items={mainNavItems} />}</SidebarContent>

            <SidebarFooter>
                <NavFooter items={footerNavItems} className="mt-auto" />
                <NavUser />
            </SidebarFooter>
        </Sidebar>
    );
}
