<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\NewsArticle;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Inertia\Inertia;
use Inertia\Response;

class NewsController extends Controller
{
    use AuthorizesRequests;

    public function index(): Response
    {
        $this->authorize('view news');

        $articles = NewsArticle::with('author')
            ->latest()
            ->paginate(15)
            ->withQueryString();

        return Inertia::render('admin/news/index', [
            'articles' => $articles,
        ]);
    }

    public function create(): Response
    {
        $this->authorize('create news');

        return Inertia::render('admin/news/create');
    }

    public function store(Request $request): RedirectResponse
    {
        $this->authorize('create news');

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:news_articles,slug',
            'excerpt' => 'nullable|string|max:500',
            'content' => 'required|string',
            'featured_image' => 'nullable|string',
            'status' => 'required|in:draft,published',
            'published_at' => 'nullable|date',
            'seo_data' => 'nullable|array',
            'seo_data.title' => 'nullable|string|max:255',
            'seo_data.description' => 'nullable|string|max:500',
            'seo_data.keywords' => 'nullable|string',
        ]);

        if (empty($validated['slug'])) {
            $validated['slug'] = Str::slug($validated['title']);
        }

        if ($validated['status'] === 'published' && empty($validated['published_at'])) {
            $validated['published_at'] = now();
        }

        $validated['created_by'] = auth()->id();

        NewsArticle::create($validated);

        return redirect()
            ->route('admin.news.index')
            ->with('success', 'Artikel berita berhasil dibuat.');
    }

    public function show(NewsArticle $news): Response
    {
        $this->authorize('view news');

        $news->load('author');

        return Inertia::render('admin/news/show', [
            'article' => $news,
        ]);
    }

    public function edit(NewsArticle $news): Response
    {
        $this->authorize('edit news');

        return Inertia::render('admin/news/edit', [
            'article' => $news,
        ]);
    }

    public function update(Request $request, NewsArticle $news): RedirectResponse
    {
        $this->authorize('edit news');

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'slug' => 'required|string|max:255|unique:news_articles,slug,'.$news->id,
            'excerpt' => 'nullable|string|max:500',
            'content' => 'required|string',
            'featured_image' => 'nullable|string',
            'status' => 'required|in:draft,published',
            'published_at' => 'nullable|date',
            'seo_data' => 'nullable|array',
            'seo_data.title' => 'nullable|string|max:255',
            'seo_data.description' => 'nullable|string|max:500',
            'seo_data.keywords' => 'nullable|string',
        ]);

        if ($validated['status'] === 'published' && empty($validated['published_at'])) {
            $validated['published_at'] = now();
        }

        $news->update($validated);

        return redirect()
            ->route('admin.news.index')
            ->with('success', 'Artikel berita berhasil diperbarui.');
    }

    public function destroy(NewsArticle $news): RedirectResponse
    {
        $this->authorize('delete news');

        $news->delete();

        return redirect()
            ->route('admin.news.index')
            ->with('success', 'Artikel berita berhasil dihapus.');
    }
}
