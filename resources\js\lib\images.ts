// Business Unit Images - Langsung pakai slug dari database
export const getBusinessUnitImage = (slug: string): string => {
    return `/assets/unit-usaha/${slug}.jpg`;
};

// Business Unit Icons - Langsung pakai ikon yang sesuai
export const getBusinessUnitIcon = (slug: string): string => {
    const icons: Record<string, string> = {
        'cigi-net': '🌐',
        'cigi-farm': '🌾',
        'cigi-food': '🍽️',
        'cigi-fc': '⚽',
        'pb-cigi': '🏸',
        'krl-cigi': '💡',
        'rt-cigi': '🏠',
        'cigi-archery': '🏹',
        'cigi-mart': '🛒',
    };
    return icons[slug] || '🏢';
};

// Business Unit Colors - Langsung pakai warna yang sesuai
export const getBusinessUnitColor = (slug: string): string => {
    const colors: Record<string, string> = {
        'cigi-net': 'from-blue-500 to-blue-600',
        'cigi-farm': 'from-green-500 to-green-600',
        'cigi-food': 'from-red-500 to-red-600',
        'cigi-fc': 'from-green-500 to-green-600',
        'pb-cigi': 'from-purple-500 to-purple-600',
        'krl-cigi': 'from-yellow-500 to-yellow-600',
        'rt-cigi': 'from-indigo-500 to-indigo-600',
        'cigi-archery': 'from-red-500 to-red-600',
        'cigi-mart': 'from-blue-500 to-blue-600',
    };
    return colors[slug] || 'from-gray-500 to-gray-600';
};

// Other Images
export const otherImages = {
    'cigi-global': '/assets/cigi-global.jpg',
    'cigi-global-2': '/assets/cigi-global-2.jpg',
    'hero-img': '/assets/hero-img.webp',
    'globe': '/assets/globe.svg',
    'favicon': '/assets/favicon.ico',
} as const;
