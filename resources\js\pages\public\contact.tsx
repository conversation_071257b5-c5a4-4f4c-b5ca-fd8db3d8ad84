import InputError from '@/components/input-error';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Head, Link, useForm } from '@inertiajs/react';
import { Building2, Mail, MapPin, Phone, Send } from 'lucide-react';

interface Props {
    settings: Record<string, string>;
}

export default function Contact({ settings }: Props) {
    const { data, setData, post, processing, errors, reset } = useForm({
        name: '',
        email: '',
        phone: '',
        subject: '',
        message: '',
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post(route('contact.submit'), {
            onSuccess: () => {
                reset();
            },
        });
    };

    return (
        <>
            <Head title="Kontak - Cigi Global">
                <meta name="description" content="Hubungi Cigi Global untuk informasi lebih lanjut tentang layanan dan unit bisnis kami" />
            </Head>

            <div className="min-h-screen bg-gray-50">
                {/* Header */}
                <header className="bg-white shadow-sm">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="flex items-center justify-between py-6">
                            <div className="flex items-center">
                                <Link href="/" className="flex items-center">
                                    <Building2 className="mr-2 h-6 w-6 text-blue-600" />
                                    <span className="text-lg font-semibold text-gray-900">Cigi Global</span>
                                </Link>
                            </div>
                            <nav className="hidden space-x-6 md:flex">
                                <Link href="/" className="text-gray-700 hover:text-blue-600">
                                    Beranda
                                </Link>
                                <Link href="/news" className="text-gray-700 hover:text-blue-600">
                                    Berita
                                </Link>
                                <Link href="/contact" className="font-medium text-blue-600">
                                    Kontak
                                </Link>
                            </nav>
                        </div>
                    </div>
                </header>

                {/* Hero Section */}
                <section className="bg-blue-600 py-16">
                    <div className="mx-auto max-w-7xl px-4 text-center text-white sm:px-6 lg:px-8">
                        <h1 className="mb-4 text-4xl font-bold md:text-5xl">Hubungi Kami</h1>
                        <p className="mx-auto max-w-3xl text-xl opacity-90">
                            Kami siap membantu Anda dengan pertanyaan tentang layanan dan unit bisnis Cigi Global
                        </p>
                    </div>
                </section>

                {/* Contact Content */}
                <section className="py-16">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="grid gap-8 lg:grid-cols-2">
                            {/* Contact Form */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Send className="h-5 w-5" />
                                        Kirim Pesan
                                    </CardTitle>
                                    <CardDescription>Isi formulir di bawah ini dan tim kami akan segera menghubungi Anda</CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <form onSubmit={handleSubmit} className="space-y-6">
                                        <div className="grid gap-4 md:grid-cols-2">
                                            <div className="space-y-2">
                                                <Label htmlFor="name">Nama Lengkap *</Label>
                                                <Input
                                                    id="name"
                                                    type="text"
                                                    value={data.name}
                                                    onChange={(e) => setData('name', e.target.value)}
                                                    placeholder="Masukkan nama lengkap"
                                                    required
                                                />
                                                <InputError message={errors.name} />
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="email">Email *</Label>
                                                <Input
                                                    id="email"
                                                    type="email"
                                                    value={data.email}
                                                    onChange={(e) => setData('email', e.target.value)}
                                                    placeholder="Masukkan alamat email"
                                                    required
                                                />
                                                <InputError message={errors.email} />
                                            </div>
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="phone">Nomor Telepon</Label>
                                            <Input
                                                id="phone"
                                                type="tel"
                                                value={data.phone}
                                                onChange={(e) => setData('phone', e.target.value)}
                                                placeholder="Masukkan nomor telepon (opsional)"
                                            />
                                            <InputError message={errors.phone} />
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="subject">Subjek *</Label>
                                            <Input
                                                id="subject"
                                                type="text"
                                                value={data.subject}
                                                onChange={(e) => setData('subject', e.target.value)}
                                                placeholder="Masukkan subjek pesan"
                                                required
                                            />
                                            <InputError message={errors.subject} />
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="message">Pesan *</Label>
                                            <Textarea
                                                id="message"
                                                value={data.message}
                                                onChange={(e) => setData('message', e.target.value)}
                                                placeholder="Tulis pesan Anda di sini..."
                                                rows={6}
                                                required
                                            />
                                            <InputError message={errors.message} />
                                        </div>

                                        <Button type="submit" disabled={processing} className="w-full bg-blue-600 hover:bg-blue-700">
                                            <Send className="mr-2 h-4 w-4" />
                                            {processing ? 'Mengirim...' : 'Kirim Pesan'}
                                        </Button>
                                    </form>
                                </CardContent>
                            </Card>

                            {/* Contact Information */}
                            <div className="space-y-6">
                                {/* Contact Details */}
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Informasi Kontak</CardTitle>
                                        <CardDescription>Hubungi kami melalui informasi di bawah ini</CardDescription>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        {settings.contact_phone && (
                                            <div className="flex items-center gap-3">
                                                <Phone className="h-5 w-5 text-muted-foreground" />
                                                <div>
                                                    <p className="font-medium">Telepon</p>
                                                    <a href={`tel:${settings.contact_phone}`} className="text-sm text-blue-600 hover:underline">
                                                        {settings.contact_phone}
                                                    </a>
                                                </div>
                                            </div>
                                        )}

                                        {settings.contact_email && (
                                            <div className="flex items-center gap-3">
                                                <Mail className="h-5 w-5 text-muted-foreground" />
                                                <div>
                                                    <p className="font-medium">Email</p>
                                                    <a href={`mailto:${settings.contact_email}`} className="text-sm text-blue-600 hover:underline">
                                                        {settings.contact_email}
                                                    </a>
                                                </div>
                                            </div>
                                        )}

                                        {settings.contact_address && (
                                            <div className="flex items-start gap-3">
                                                <MapPin className="mt-0.5 h-5 w-5 text-muted-foreground" />
                                                <div>
                                                    <p className="font-medium">Alamat</p>
                                                    <p className="text-sm text-muted-foreground">{settings.contact_address}</p>
                                                </div>
                                            </div>
                                        )}
                                    </CardContent>
                                </Card>

                                {/* Business Units */}
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Unit Bisnis Kami</CardTitle>
                                        <CardDescription>Untuk pertanyaan spesifik tentang unit bisnis tertentu</CardDescription>
                                    </CardHeader>
                                    <CardContent>
                                        <p className="mb-4 text-sm text-muted-foreground">
                                            Jika Anda memiliki pertanyaan khusus tentang salah satu unit bisnis kami, Anda dapat mengunjungi halaman
                                            unit bisnis tersebut untuk informasi kontak yang lebih spesifik.
                                        </p>
                                        <Link href="/">
                                            <Button variant="outline" className="w-full">
                                                <Building2 className="mr-2 h-4 w-4" />
                                                Lihat Semua Unit Bisnis
                                            </Button>
                                        </Link>
                                    </CardContent>
                                </Card>

                                {/* Office Hours */}
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Jam Operasional</CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="space-y-2 text-sm">
                                            <div className="flex justify-between">
                                                <span>Senin - Jumat</span>
                                                <span>08:00 - 17:00 WIB</span>
                                            </div>
                                            <div className="flex justify-between">
                                                <span>Sabtu</span>
                                                <span>08:00 - 12:00 WIB</span>
                                            </div>
                                            <div className="flex justify-between">
                                                <span>Minggu</span>
                                                <span>Tutup</span>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            </div>
                        </div>
                    </div>
                </section>

                {/* Footer */}
                <footer className="bg-gray-900 py-12 text-white">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="text-center">
                            <div className="mb-4 flex items-center justify-center">
                                <Building2 className="mr-2 h-6 w-6" />
                                <h3 className="text-xl font-bold">Cigi Global</h3>
                            </div>
                            <p className="mx-auto mb-6 max-w-2xl text-gray-400">
                                {settings.site_description ||
                                    'Pusat bisnis terpadu yang mengembangkan berbagai unit usaha untuk kemajuan masyarakat desa.'}
                            </p>
                            <div className="flex justify-center space-x-6">
                                <Link href="/" className="text-gray-400 hover:text-white">
                                    Beranda
                                </Link>
                                <Link href="/news" className="text-gray-400 hover:text-white">
                                    Berita
                                </Link>
                                <Link href="/contact" className="text-gray-400 hover:text-white">
                                    Kontak
                                </Link>
                            </div>
                        </div>
                    </div>
                </footer>
            </div>
        </>
    );
}
