import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/react';
import { Building2, Eye, FileText, Palette, Plus, Settings } from 'lucide-react';

interface BusinessUnit {
    id: number;
    name: string;
    slug: string;
    description: string;
    status: string;
    order: number;
    pages_count: number;
    galleries_count: number;
    contact_info: {
        phone?: string;
        email?: string;
        address?: string;
    };
    social_media: {
        instagram?: string;
        facebook?: string;
    };
    primary_color?: string;
    created_at: string;
}

interface Props {
    business_units: BusinessUnit[];
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Admin',
        href: '/admin',
    },
    {
        title: 'Unit Bisnis',
        href: '/admin/business-units',
    },
];

export default function BusinessUnitsIndex({ business_units }: Props) {
    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'active':
                return <Badge className="bg-green-100 text-green-800">Aktif</Badge>;
            case 'coming_soon':
                return <Badge className="bg-blue-100 text-blue-800">Akan Datang</Badge>;
            case 'inactive':
                return <Badge className="bg-gray-100 text-gray-800">Tidak Aktif</Badge>;
            default:
                return <Badge>{status}</Badge>;
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Kelola Unit Bisnis" />

            <div className="flex h-full flex-1 flex-col gap-4 p-4">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold tracking-tight">Unit Bisnis</h1>
                        <p className="text-muted-foreground">Kelola semua unit bisnis Cigi Global</p>
                    </div>
                    <Link href="/admin/business-units/create">
                        <Button>
                            <Plus className="mr-2 h-4 w-4" />
                            Tambah Unit Bisnis
                        </Button>
                    </Link>
                </div>

                {/* Business Units Grid */}
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    {business_units.map((unit) => (
                        <Card key={unit.id} className="relative">
                            <CardHeader className="pb-3">
                                <div className="flex items-start justify-between">
                                    <div className="flex items-center gap-2">
                                        <Building2 className="h-5 w-5 text-muted-foreground" />
                                        <div>
                                            <CardTitle className="text-lg">{unit.name}</CardTitle>
                                            <CardDescription>/{unit.slug}</CardDescription>
                                        </div>
                                    </div>
                                    {unit.primary_color && (
                                        <div
                                            className="h-4 w-4 rounded-full border border-gray-200"
                                            style={{ backgroundColor: unit.primary_color }}
                                            title={unit.primary_color}
                                        />
                                    )}
                                </div>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div>
                                    <p className="line-clamp-2 text-sm text-muted-foreground">{unit.description}</p>
                                </div>

                                <div className="flex items-center justify-between">
                                    {getStatusBadge(unit.status)}
                                    <span className="text-xs text-muted-foreground">Urutan: {unit.order}</span>
                                </div>

                                <div className="grid grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <span className="text-muted-foreground">Halaman:</span>
                                        <span className="ml-1 font-medium">{unit.pages_count}</span>
                                    </div>
                                    <div>
                                        <span className="text-muted-foreground">Galeri:</span>
                                        <span className="ml-1 font-medium">{unit.galleries_count}</span>
                                    </div>
                                </div>

                                {(unit.contact_info?.email || unit.contact_info?.phone) && (
                                    <div className="space-y-1">
                                        {unit.contact_info.email && <p className="text-xs text-muted-foreground">📧 {unit.contact_info.email}</p>}
                                        {unit.contact_info.phone && <p className="text-xs text-muted-foreground">📞 {unit.contact_info.phone}</p>}
                                    </div>
                                )}

                                <div className="flex gap-2 pt-2">
                                    <Link href={`/admin/business-units/${unit.id}`}>
                                        <Button variant="outline" size="sm">
                                            <Eye className="mr-1 h-3 w-3" />
                                            Lihat
                                        </Button>
                                    </Link>
                                    <Link href={`/admin/business-units/${unit.id}/pages`}>
                                        <Button variant="outline" size="sm">
                                            <FileText className="mr-1 h-3 w-3" />
                                            Halaman
                                        </Button>
                                    </Link>
                                    <Link href={`/admin/business-units/${unit.id}/edit`}>
                                        <Button variant="outline" size="sm">
                                            <Settings className="mr-1 h-3 w-3" />
                                            Edit
                                        </Button>
                                    </Link>
                                    <Link href={`/${unit.slug}`} target="_blank">
                                        <Button variant="outline" size="sm">
                                            <Palette className="mr-1 h-3 w-3" />
                                            Preview
                                        </Button>
                                    </Link>
                                </div>
                            </CardContent>
                        </Card>
                    ))}
                </div>

                {business_units.length === 0 && (
                    <Card>
                        <CardContent className="flex flex-col items-center justify-center py-12">
                            <Building2 className="mb-4 h-12 w-12 text-muted-foreground" />
                            <h3 className="mb-2 text-lg font-medium">Belum ada unit bisnis</h3>
                            <p className="mb-4 text-center text-muted-foreground">Mulai dengan membuat unit bisnis pertama untuk Cigi Global</p>
                            <Link href="/admin/business-units/create">
                                <Button>
                                    <Plus className="mr-2 h-4 w-4" />
                                    Buat Unit Bisnis Pertama
                                </Button>
                            </Link>
                        </CardContent>
                    </Card>
                )}
            </div>
        </AppLayout>
    );
}
