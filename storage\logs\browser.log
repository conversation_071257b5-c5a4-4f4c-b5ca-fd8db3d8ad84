[2025-08-21 12:46:36] local.ERROR: Unhandled Promise Rejection Error Page not found: ./pages/public/home.tsx resolvePageComponent@http://[::1]:5173/node_modules/.vite/deps/laravel-vite-plugin_inertia-helpers.js?v=b7772b58:12:9
resolve@http://[::1]:5173/resources/js/app.tsx:10:42
resolveComponent@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=c166861c:14328:54
createInertiaApp@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=c166861c:14331:5
@http://[::1]:5173/resources/js/app.tsx:8:17 {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T12:46:36.765Z"} 
[2025-08-21 12:53:22] local.ERROR: Unhandled Promise Rejection Error Page not found: ./pages/public/home.tsx Error: Page not found: ./pages/public/home.tsx
    at resolvePageComponent (http://[::1]:5173/node_modules/.vite/deps/laravel-vite-plugin_inertia-helpers.js?v=57213f17:12:9)
    at resolve (http://[::1]:5173/resources/js/app.tsx:10:22)
    at resolveComponent (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:14350:54)
    at createInertiaApp (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:14353:5)
    at http://[::1]:5173/resources/js/app.tsx:8:1 {"url":"http://cigi-global.test/?herd=preview","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Herd/1.22.1 Chrome/120.0.6099.291 Electron/28.2.5 Safari/537.36","timestamp":"2025-08-21T12:53:22.412Z"} 
[2025-08-21 12:53:27] local.ERROR: Unhandled Promise Rejection Error Page not found: ./pages/public/home.tsx resolvePageComponent@http://[::1]:5173/node_modules/.vite/deps/laravel-vite-plugin_inertia-helpers.js?v=57213f17:12:9
resolve@http://[::1]:5173/resources/js/app.tsx:10:42
resolveComponent@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:14350:54
createInertiaApp@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:14353:5
@http://[::1]:5173/resources/js/app.tsx:8:17 {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T12:53:27.042Z"} 
[2025-08-21 12:54:14] local.ERROR: Unhandled Promise Rejection Error Page not found: ./pages/public/home.tsx Error: Page not found: ./pages/public/home.tsx
    at resolvePageComponent (http://[::1]:5173/node_modules/.vite/deps/laravel-vite-plugin_inertia-helpers.js?v=57213f17:12:9)
    at resolve (http://[::1]:5173/resources/js/app.tsx:10:22)
    at resolveComponent (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:14350:54)
    at createInertiaApp (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:14353:5)
    at http://[::1]:5173/resources/js/app.tsx:8:1 {"url":"http://cigi-global.test/?herd=preview","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Herd/1.22.1 Chrome/120.0.6099.291 Electron/28.2.5 Safari/537.36","timestamp":"2025-08-21T12:54:14.600Z"} 
[2025-08-21 12:54:16] local.ERROR: Unhandled Promise Rejection Error Page not found: ./pages/public/home.tsx resolvePageComponent@http://[::1]:5173/node_modules/.vite/deps/laravel-vite-plugin_inertia-helpers.js?v=57213f17:12:9
resolve@http://[::1]:5173/resources/js/app.tsx:10:42
resolveComponent@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:14350:54
createInertiaApp@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:14353:5
@http://[::1]:5173/resources/js/app.tsx:8:17 {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T12:54:15.301Z"} 
[2025-08-21 12:56:22] local.ERROR: Unhandled Promise Rejection Error Page not found: ./pages/public/home.tsx Error: Page not found: ./pages/public/home.tsx
    at resolvePageComponent (http://[::1]:5173/node_modules/.vite/deps/laravel-vite-plugin_inertia-helpers.js?v=57213f17:12:9)
    at resolve (http://[::1]:5173/resources/js/app.tsx:10:22)
    at resolveComponent (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:14350:54)
    at createInertiaApp (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:14353:5)
    at http://[::1]:5173/resources/js/app.tsx:8:1 {"url":"http://cigi-global.test/?herd=preview","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Herd/1.22.1 Chrome/120.0.6099.291 Electron/28.2.5 Safari/537.36","timestamp":"2025-08-21T12:56:22.274Z"} 
[2025-08-21 12:56:24] local.ERROR: Unhandled Promise Rejection Error Page not found: ./pages/public/home.tsx resolvePageComponent@http://[::1]:5173/node_modules/.vite/deps/laravel-vite-plugin_inertia-helpers.js?v=57213f17:12:9
resolve@http://[::1]:5173/resources/js/app.tsx:10:42
resolveComponent@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:14350:54
createInertiaApp@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:14353:5
@http://[::1]:5173/resources/js/app.tsx:8:17 {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T12:56:23.457Z"} 
[2025-08-21 12:56:32] local.ERROR: Unhandled Promise Rejection Error Page not found: ./pages/public/home.tsx Error: Page not found: ./pages/public/home.tsx
    at resolvePageComponent (http://[::1]:5173/node_modules/.vite/deps/laravel-vite-plugin_inertia-helpers.js?v=57213f17:12:9)
    at resolve (http://[::1]:5173/resources/js/app.tsx:10:22)
    at resolveComponent (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:14350:54)
    at createInertiaApp (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:14353:5)
    at http://[::1]:5173/resources/js/app.tsx:8:1 {"url":"http://cigi-global.test/?herd=preview","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Herd/1.22.1 Chrome/120.0.6099.291 Electron/28.2.5 Safari/537.36","timestamp":"2025-08-21T12:56:32.162Z"} 
[2025-08-21 12:56:33] local.ERROR: Unhandled Promise Rejection Error Page not found: ./pages/public/home.tsx resolvePageComponent@http://[::1]:5173/node_modules/.vite/deps/laravel-vite-plugin_inertia-helpers.js?v=57213f17:12:9
resolve@http://[::1]:5173/resources/js/app.tsx:10:42
resolveComponent@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:14350:54
createInertiaApp@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:14353:5
@http://[::1]:5173/resources/js/app.tsx:8:17 {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T12:56:32.814Z"} 
[2025-08-21 12:57:12] local.ERROR: Unhandled Promise Rejection Error Page not found: ./pages/public/home.tsx Error: Page not found: ./pages/public/home.tsx
    at resolvePageComponent (http://[::1]:5173/node_modules/.vite/deps/laravel-vite-plugin_inertia-helpers.js?v=57213f17:12:9)
    at resolve (http://[::1]:5173/resources/js/app.tsx:10:22)
    at resolveComponent (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:14350:54)
    at createInertiaApp (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:14353:5)
    at http://[::1]:5173/resources/js/app.tsx:8:1 {"url":"http://cigi-global.test/?herd=preview","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Herd/1.22.1 Chrome/120.0.6099.291 Electron/28.2.5 Safari/537.36","timestamp":"2025-08-21T12:57:11.948Z"} 
[2025-08-21 12:57:13] local.ERROR: Unhandled Promise Rejection Error Page not found: ./pages/public/home.tsx resolvePageComponent@http://[::1]:5173/node_modules/.vite/deps/laravel-vite-plugin_inertia-helpers.js?v=57213f17:12:9
resolve@http://[::1]:5173/resources/js/app.tsx:10:42
resolveComponent@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:14350:54
createInertiaApp@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:14353:5
@http://[::1]:5173/resources/js/app.tsx:8:17 {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T12:57:13.125Z"} 
[2025-08-21 12:57:59] local.ERROR: Unhandled Promise Rejection Error Page not found: ./pages/public/home.tsx Error: Page not found: ./pages/public/home.tsx
    at resolvePageComponent (http://[::1]:5173/node_modules/.vite/deps/laravel-vite-plugin_inertia-helpers.js?v=57213f17:12:9)
    at resolve (http://[::1]:5173/resources/js/app.tsx:10:22)
    at resolveComponent (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:14350:54)
    at createInertiaApp (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:14353:5)
    at http://[::1]:5173/resources/js/app.tsx:8:1 {"url":"http://cigi-global.test/?herd=preview","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Herd/1.22.1 Chrome/120.0.6099.291 Electron/28.2.5 Safari/537.36","timestamp":"2025-08-21T12:57:59.248Z"} 
[2025-08-21 12:58:01] local.ERROR: Unhandled Promise Rejection Error Page not found: ./pages/public/home.tsx resolvePageComponent@http://[::1]:5173/node_modules/.vite/deps/laravel-vite-plugin_inertia-helpers.js?v=57213f17:12:9
resolve@http://[::1]:5173/resources/js/app.tsx:10:42
resolveComponent@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:14350:54
createInertiaApp@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:14353:5
@http://[::1]:5173/resources/js/app.tsx:8:17 {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T12:58:00.314Z"} 
[2025-08-21 13:07:44] local.ERROR: Unhandled Promise Rejection Error Page not found: ./pages/public/news/index.tsx resolvePageComponent@http://[::1]:5173/node_modules/.vite/deps/laravel-vite-plugin_inertia-helpers.js?v=57213f17:12:9
resolve@http://[::1]:5173/resources/js/app.tsx:10:42
resolveComponent@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:14350:54
resolve@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:12072:33
set@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:12002:17
setPage@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:13053:17 {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T13:07:44.217Z"} 
[2025-08-21 13:07:48] local.ERROR: Unhandled Promise Rejection Error Page not found: ./pages/public/news/index.tsx resolvePageComponent@http://[::1]:5173/node_modules/.vite/deps/laravel-vite-plugin_inertia-helpers.js?v=57213f17:12:9
resolve@http://[::1]:5173/resources/js/app.tsx:10:42
resolveComponent@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:14350:54
resolve@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:12072:33
set@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:12002:17
setPage@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:13053:17 {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T13:07:48.325Z"} 
[2025-08-21 13:08:06] local.ERROR: Unhandled Promise Rejection Error Page not found: ./pages/public/news/index.tsx resolvePageComponent@http://[::1]:5173/node_modules/.vite/deps/laravel-vite-plugin_inertia-helpers.js?v=57213f17:12:9
resolve@http://[::1]:5173/resources/js/app.tsx:10:42
resolveComponent@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:14350:54
resolve@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:12072:33
set@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:12002:17
setPage@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:13053:17 {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T13:08:06.431Z"} 
[2025-08-21 13:08:11] local.ERROR: Unhandled Promise Rejection Error Page not found: ./pages/public/news/index.tsx resolvePageComponent@http://[::1]:5173/node_modules/.vite/deps/laravel-vite-plugin_inertia-helpers.js?v=57213f17:12:9
resolve@http://[::1]:5173/resources/js/app.tsx:10:42
resolveComponent@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:14350:54
resolve@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:12072:33
set@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:12002:17
setPage@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:13053:17
async*process@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:12980:16
async*handle/<@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:12965:34
processNext@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:12106:30
process@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:12098:62
add@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:12095:17
handle@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:12965:19
send/<@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:13205:28
promise callback*send@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:13203:8
send@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:13280:13
visit@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:13412:21
onClick@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:15040:18
executeDispatch@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=b0e2dbe8:11736:19
runWithFiberInDEV@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=b0e2dbe8:1487:15
processDispatchQueue@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=b0e2dbe8:11772:37
node_modules/react-dom/cjs/react-dom-client.development.js/dispatchEventForPluginEventSystem/<@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=b0e2dbe8:12182:31
batchedUpdates$1@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=b0e2dbe8:2628:44
dispatchEventForPluginEventSystem@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=b0e2dbe8:11877:25
dispatchEvent@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=b0e2dbe8:14792:46
dispatchDiscreteEvent@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=b0e2dbe8:14773:62 {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T13:08:11.473Z"} 
[2025-08-21 13:08:12] local.ERROR: Unhandled Promise Rejection Error Page not found: ./pages/public/news/index.tsx resolvePageComponent@http://[::1]:5173/node_modules/.vite/deps/laravel-vite-plugin_inertia-helpers.js?v=57213f17:12:9
resolve@http://[::1]:5173/resources/js/app.tsx:10:42
resolveComponent@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:14350:54
resolve@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:12072:33
set@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:12002:17
setPage@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:13053:17
async*process@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:12980:16
async*handle/<@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:12965:34
processNext@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:12106:30
process@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:12098:62
add@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:12095:17
handle@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:12965:19
send/<@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:13205:28
promise callback*send@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:13203:8
send@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:13280:13
visit@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:13412:21
onClick@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=f08f915b:15040:18
executeDispatch@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=b0e2dbe8:11736:19
runWithFiberInDEV@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=b0e2dbe8:1487:15
processDispatchQueue@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=b0e2dbe8:11772:37
node_modules/react-dom/cjs/react-dom-client.development.js/dispatchEventForPluginEventSystem/<@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=b0e2dbe8:12182:31
batchedUpdates$1@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=b0e2dbe8:2628:44
dispatchEventForPluginEventSystem@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=b0e2dbe8:11877:25
dispatchEvent@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=b0e2dbe8:14792:46
dispatchDiscreteEvent@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=b0e2dbe8:14773:62 {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T13:08:11.919Z"} 
[2025-08-21 13:10:31] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://cigi-global.test/?herd=preview","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Herd/1.22.1 Chrome/120.0.6099.291 Electron/28.2.5 Safari/537.36","timestamp":"2025-08-21T13:10:30.939Z"} 
[2025-08-21 13:13:20] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://cigi-global.test/?herd=preview","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Herd/1.22.1 Chrome/120.0.6099.291 Electron/28.2.5 Safari/537.36","timestamp":"2025-08-21T13:13:20.378Z"} 
[2025-08-21 13:28:03] local.ERROR: [vite] failed to connect to websocket.
your current setup:
  (browser) [::1]:5173/ <--[HTTP]--> localhost:5173/ (server)
  (browser) [::1]:5173/ <--[WebSocket (failing)]--> localhost:5173/ (server)
Check out your Vite / network configuration and https://vite.dev/config/server-options.html#server-hmr . {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T13:28:03.462Z"} 
[2025-08-21 13:36:37] local.ERROR: SyntaxError: redeclaration of let exceptionAsMarkdown http://cigi-global.test/login line 12932 > injectedScript 4287 1 SyntaxError redeclaration of let exceptionAsMarkdown @http://cigi-global.test/login line 12932 > injectedScript:4287:1
show@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12932:35
handleNonInertiaResponse@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:13006:28
process@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12976:19
handle/<@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12965:34
processNext@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12106:30
process@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12098:62
add@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12095:17
handle@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12965:19
send/<@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:13209:30
promise callback*send@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:13206:8
send@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:13280:13
visit@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:13412:21
post@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:13336:17
useForm/submit<@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:14552:24
submit@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:14783:12
onSubmit@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:14814:11
executeDispatch@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11736:19
runWithFiberInDEV@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:1487:15
processDispatchQueue@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11772:37
node_modules/react-dom/cjs/react-dom-client.development.js/dispatchEventForPluginEventSystem/<@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:12182:31
batchedUpdates$1@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:2628:44
dispatchEventForPluginEventSystem@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11877:25
dispatchEvent@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:14792:46
dispatchDiscreteEvent@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:14773:62
EventListener.handleEvent*addTrappedEventListener@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11842:30
listenToNativeEvent@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11801:32
node_modules/react-dom/cjs/react-dom-client.development.js/listenToAllSupportedEvents/<@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11812:92
listenToAllSupportedEvents@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11811:27
node_modules/react-dom/cjs/react-dom-client.development.js/exports.createRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:18025:35
setup@http://[::1]:5173/resources/js/app.tsx:12:28
createInertiaApp/reactApp<@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:14357:12
promise callback*createInertiaApp@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:14356:6
@http://[::1]:5173/resources/js/app.tsx:8:17 {"url":"http://cigi-global.test/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T13:36:36.747Z"} 
[2025-08-21 13:36:37] local.ERROR: SyntaxError: redeclaration of let exceptionAsMarkdown http://cigi-global.test/login line 12932 > injectedScript 4287 1 SyntaxError redeclaration of let exceptionAsMarkdown @http://cigi-global.test/login line 12932 > injectedScript:4287:1
show@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12932:35
handleNonInertiaResponse@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:13006:28
process@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12976:19
handle/<@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12965:34
processNext@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12106:30
process@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12098:62
add@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12095:17
handle@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12965:19
send/<@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:13209:30
promise callback*send@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:13206:8
send@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:13280:13
visit@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:13412:21
post@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:13336:17
useForm/submit<@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:14552:24
submit@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:14783:12
onSubmit@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:14814:11
executeDispatch@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11736:19
runWithFiberInDEV@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:1487:15
processDispatchQueue@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11772:37
node_modules/react-dom/cjs/react-dom-client.development.js/dispatchEventForPluginEventSystem/<@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:12182:31
batchedUpdates$1@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:2628:44
dispatchEventForPluginEventSystem@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11877:25
dispatchEvent@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:14792:46
dispatchDiscreteEvent@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:14773:62
EventListener.handleEvent*addTrappedEventListener@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11842:30
listenToNativeEvent@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11801:32
node_modules/react-dom/cjs/react-dom-client.development.js/listenToAllSupportedEvents/<@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11812:92
listenToAllSupportedEvents@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11811:27
node_modules/react-dom/cjs/react-dom-client.development.js/exports.createRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:18025:35
setup@http://[::1]:5173/resources/js/app.tsx:12:28
createInertiaApp/reactApp<@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:14357:12
promise callback*createInertiaApp@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:14356:6
@http://[::1]:5173/resources/js/app.tsx:8:17 {"url":"http://cigi-global.test/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T13:36:36.747Z"} 
[2025-08-21 13:36:37] local.ERROR: SyntaxError: redeclaration of let pi http://cigi-global.test/login line 12932 > injectedScript 5305 1 SyntaxError redeclaration of let pi @http://cigi-global.test/login line 12932 > injectedScript:5305:1
show@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12932:35
handleNonInertiaResponse@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:13006:28
process@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12976:19
handle/<@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12965:34
processNext@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12106:30
process@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12098:62
add@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12095:17
handle@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12965:19
send/<@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:13209:30
promise callback*send@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:13206:8
send@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:13280:13
visit@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:13412:21
post@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:13336:17
useForm/submit<@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:14552:24
submit@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:14783:12
onSubmit@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:14814:11
executeDispatch@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11736:19
runWithFiberInDEV@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:1487:15
processDispatchQueue@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11772:37
node_modules/react-dom/cjs/react-dom-client.development.js/dispatchEventForPluginEventSystem/<@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:12182:31
batchedUpdates$1@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:2628:44
dispatchEventForPluginEventSystem@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11877:25
dispatchEvent@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:14792:46
dispatchDiscreteEvent@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:14773:62
EventListener.handleEvent*addTrappedEventListener@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11842:30
listenToNativeEvent@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11801:32
node_modules/react-dom/cjs/react-dom-client.development.js/listenToAllSupportedEvents/<@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11812:92
listenToAllSupportedEvents@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11811:27
node_modules/react-dom/cjs/react-dom-client.development.js/exports.createRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:18025:35
setup@http://[::1]:5173/resources/js/app.tsx:12:28
createInertiaApp/reactApp<@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:14357:12
promise callback*createInertiaApp@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:14356:6
@http://[::1]:5173/resources/js/app.tsx:8:17 {"url":"http://cigi-global.test/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T13:36:36.758Z"} 
[2025-08-21 13:36:37] local.ERROR: SyntaxError: redeclaration of let pi http://cigi-global.test/login line 12932 > injectedScript 5305 1 SyntaxError redeclaration of let pi @http://cigi-global.test/login line 12932 > injectedScript:5305:1
show@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12932:35
handleNonInertiaResponse@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:13006:28
process@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12976:19
handle/<@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12965:34
processNext@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12106:30
process@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12098:62
add@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12095:17
handle@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12965:19
send/<@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:13209:30
promise callback*send@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:13206:8
send@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:13280:13
visit@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:13412:21
post@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:13336:17
useForm/submit<@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:14552:24
submit@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:14783:12
onSubmit@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:14814:11
executeDispatch@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11736:19
runWithFiberInDEV@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:1487:15
processDispatchQueue@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11772:37
node_modules/react-dom/cjs/react-dom-client.development.js/dispatchEventForPluginEventSystem/<@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:12182:31
batchedUpdates$1@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:2628:44
dispatchEventForPluginEventSystem@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11877:25
dispatchEvent@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:14792:46
dispatchDiscreteEvent@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:14773:62
EventListener.handleEvent*addTrappedEventListener@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11842:30
listenToNativeEvent@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11801:32
node_modules/react-dom/cjs/react-dom-client.development.js/listenToAllSupportedEvents/<@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11812:92
listenToAllSupportedEvents@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11811:27
node_modules/react-dom/cjs/react-dom-client.development.js/exports.createRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:18025:35
setup@http://[::1]:5173/resources/js/app.tsx:12:28
createInertiaApp/reactApp<@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:14357:12
promise callback*createInertiaApp@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:14356:6
@http://[::1]:5173/resources/js/app.tsx:8:17 {"url":"http://cigi-global.test/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T13:36:36.758Z"} 
[2025-08-21 13:38:41] local.ERROR: SyntaxError: redeclaration of let exceptionAsMarkdown http://cigi-global.test/dashboard line 12932 > injectedScript 4287 1 SyntaxError redeclaration of let exceptionAsMarkdown @http://cigi-global.test/dashboard line 12932 > injectedScript:4287:1
show@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12932:35
handleNonInertiaResponse@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:13006:28
process@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12976:19
handle/<@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12965:34
processNext@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12106:30
process@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12098:62
add@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12095:17
handle@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12965:19
use/<@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12710:23 {"url":"http://cigi-global.test/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T13:38:41.636Z"} 
[2025-08-21 13:38:41] local.ERROR: SyntaxError: redeclaration of let exceptionAsMarkdown http://cigi-global.test/dashboard line 12932 > injectedScript 4287 1 SyntaxError redeclaration of let exceptionAsMarkdown @http://cigi-global.test/dashboard line 12932 > injectedScript:4287:1
show@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12932:35
handleNonInertiaResponse@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:13006:28
process@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12976:19
handle/<@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12965:34
processNext@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12106:30
process@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12098:62
add@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12095:17
handle@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12965:19
use/<@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12710:23 {"url":"http://cigi-global.test/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T13:38:41.637Z"} 
[2025-08-21 13:38:41] local.ERROR: SyntaxError: redeclaration of let pi http://cigi-global.test/dashboard line 12932 > injectedScript 5305 1 SyntaxError redeclaration of let pi @http://cigi-global.test/dashboard line 12932 > injectedScript:5305:1
show@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12932:35
handleNonInertiaResponse@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:13006:28
process@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12976:19
handle/<@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12965:34
processNext@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12106:30
process@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12098:62
add@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12095:17
handle@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12965:19
use/<@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12710:23 {"url":"http://cigi-global.test/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T13:38:41.647Z"} 
[2025-08-21 13:38:41] local.ERROR: SyntaxError: redeclaration of let pi http://cigi-global.test/dashboard line 12932 > injectedScript 5305 1 SyntaxError redeclaration of let pi @http://cigi-global.test/dashboard line 12932 > injectedScript:5305:1
show@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12932:35
handleNonInertiaResponse@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:13006:28
process@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12976:19
handle/<@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12965:34
processNext@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12106:30
process@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12098:62
add@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12095:17
handle@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12965:19
use/<@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12710:23 {"url":"http://cigi-global.test/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T13:38:41.647Z"} 
[2025-08-21 13:41:36] local.ERROR: Unhandled Promise Rejection Error Page not found: ./pages/admin/users/index.tsx resolvePageComponent@http://[::1]:5173/node_modules/.vite/deps/laravel-vite-plugin_inertia-helpers.js?v=80113ab5:12:9
resolve@http://[::1]:5173/resources/js/app.tsx:10:42
resolveComponent@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:14350:54
resolve@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12072:33
set@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12002:17
setPage@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:13053:17 {"url":"http://cigi-global.test/admin","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T13:41:36.540Z"} 
[2025-08-21 13:41:41] local.ERROR: Unhandled Promise Rejection Error Page not found: ./pages/admin/users/index.tsx resolvePageComponent@http://[::1]:5173/node_modules/.vite/deps/laravel-vite-plugin_inertia-helpers.js?v=80113ab5:12:9
resolve@http://[::1]:5173/resources/js/app.tsx:10:42
resolveComponent@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:14350:54
resolve@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12072:33
set@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12002:17
setPage@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:13053:17 {"url":"http://cigi-global.test/admin","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T13:41:41.634Z"} 
[2025-08-21 13:41:42] local.ERROR: Unhandled Promise Rejection Error Page not found: ./pages/admin/users/index.tsx resolvePageComponent@http://[::1]:5173/node_modules/.vite/deps/laravel-vite-plugin_inertia-helpers.js?v=80113ab5:12:9
resolve@http://[::1]:5173/resources/js/app.tsx:10:42
resolveComponent@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:14350:54
resolve@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12072:33
set@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12002:17
setPage@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:13053:17 {"url":"http://cigi-global.test/admin","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T13:41:42.269Z"} 
[2025-08-21 13:41:42] local.ERROR: Unhandled Promise Rejection Error Page not found: ./pages/admin/users/index.tsx resolvePageComponent@http://[::1]:5173/node_modules/.vite/deps/laravel-vite-plugin_inertia-helpers.js?v=80113ab5:12:9
resolve@http://[::1]:5173/resources/js/app.tsx:10:42
resolveComponent@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:14350:54
resolve@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12072:33
set@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12002:17
setPage@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:13053:17 {"url":"http://cigi-global.test/admin","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T13:41:42.498Z"} 
[2025-08-21 13:56:26] local.ERROR: Error: A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder. http://[::1]:5173/node_modules/.vite/deps/@radix-ui_react-select.js?v=80113ab5 869 13 Error A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder. SelectItem<@http://[::1]:5173/node_modules/.vite/deps/@radix-ui_react-select.js?v=80113ab5:869:13
react_stack_bottom_frame@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:17424:20
renderWithHooks@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:4206:42
updateForwardRef@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:6461:21
beginWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:7864:20
runWithFiberInDEV@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:1487:15
performUnitOfWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10868:98
workLoopSync@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10728:60
renderRootSync@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10711:13
performWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10359:46
performSyncWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11635:26
flushSyncWorkAcrossRoots_impl@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11536:122
flushSpawnedWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11254:40
commitRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11081:11
commitRootWhenReady@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10512:19
performWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10457:36
performWorkOnRootViaSchedulerTask@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11623:26
performWorkUntilDeadline@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:36:58 {"url":"http://cigi-global.test/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T13:56:26.305Z"} 
[2025-08-21 13:56:26] local.ERROR: Error: A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder. http://[::1]:5173/node_modules/.vite/deps/@radix-ui_react-select.js?v=80113ab5 869 13 Error A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder. SelectItem<@http://[::1]:5173/node_modules/.vite/deps/@radix-ui_react-select.js?v=80113ab5:869:13
react_stack_bottom_frame@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:17424:20
renderWithHooks@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:4206:42
updateForwardRef@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:6461:21
beginWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:7864:20
runWithFiberInDEV@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:1487:15
performUnitOfWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10868:98
workLoopSync@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10728:60
renderRootSync@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10711:13
performWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10359:46
performSyncWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11635:26
flushSyncWorkAcrossRoots_impl@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11536:122
flushSpawnedWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11254:40
commitRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11081:11
commitRootWhenReady@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10512:19
performWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10457:36
performWorkOnRootViaSchedulerTask@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11623:26
performWorkUntilDeadline@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:36:58 {"url":"http://cigi-global.test/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T13:56:26.305Z"} 
[2025-08-21 13:56:26] local.WARNING: %s

%s An error occurred in the <SelectItem> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://cigi-global.test/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T13:56:26.305Z"} 
[2025-08-21 13:56:32] local.ERROR: Error: A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder. http://[::1]:5173/node_modules/.vite/deps/@radix-ui_react-select.js?v=80113ab5 869 13 Error A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder. SelectItem<@http://[::1]:5173/node_modules/.vite/deps/@radix-ui_react-select.js?v=80113ab5:869:13
react_stack_bottom_frame@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:17424:20
renderWithHooks@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:4206:42
updateForwardRef@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:6461:21
beginWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:7864:20
runWithFiberInDEV@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:1487:15
performUnitOfWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10868:98
workLoopSync@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10728:60
renderRootSync@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10711:13
performWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10359:46
performSyncWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11635:26
flushSyncWorkAcrossRoots_impl@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11536:122
flushSpawnedWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11254:40
commitRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11081:11
commitRootWhenReady@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10512:19
performWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10457:36
performWorkOnRootViaSchedulerTask@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11623:26
performWorkUntilDeadline@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:36:58 {"url":"http://cigi-global.test/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T13:56:32.654Z"} 
[2025-08-21 13:56:32] local.ERROR: Error: A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder. http://[::1]:5173/node_modules/.vite/deps/@radix-ui_react-select.js?v=80113ab5 869 13 Error A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder. SelectItem<@http://[::1]:5173/node_modules/.vite/deps/@radix-ui_react-select.js?v=80113ab5:869:13
react_stack_bottom_frame@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:17424:20
renderWithHooks@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:4206:42
updateForwardRef@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:6461:21
beginWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:7864:20
runWithFiberInDEV@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:1487:15
performUnitOfWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10868:98
workLoopSync@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10728:60
renderRootSync@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10711:13
performWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10359:46
performSyncWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11635:26
flushSyncWorkAcrossRoots_impl@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11536:122
flushSpawnedWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11254:40
commitRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11081:11
commitRootWhenReady@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10512:19
performWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10457:36
performWorkOnRootViaSchedulerTask@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11623:26
performWorkUntilDeadline@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:36:58 {"url":"http://cigi-global.test/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T13:56:32.654Z"} 
[2025-08-21 13:56:32] local.WARNING: %s

%s An error occurred in the <SelectItem> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://cigi-global.test/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T13:56:32.655Z"} 
[2025-08-21 13:58:57] local.ERROR: Unhandled Promise Rejection SyntaxError The requested module 'http://[::1]:5173/node_modules/.vite/deps/lucide-react.js?v=80113ab5' doesn't provide an export named: 'UserEdit' null {"url":"http://cigi-global.test/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T13:58:57.151Z"} 
[2025-08-21 13:59:13] local.ERROR: Unhandled Promise Rejection SyntaxError The requested module 'http://[::1]:5173/node_modules/.vite/deps/lucide-react.js?v=80113ab5' doesn't provide an export named: 'UserEdit' null {"url":"http://cigi-global.test/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T13:59:13.536Z"} 
[2025-08-21 13:59:16] local.ERROR: Unhandled Promise Rejection SyntaxError The requested module 'http://[::1]:5173/node_modules/.vite/deps/lucide-react.js?v=80113ab5' doesn't provide an export named: 'UserEdit' null {"url":"http://cigi-global.test/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T13:59:16.090Z"} 
[2025-08-21 13:59:17] local.ERROR: Unhandled Promise Rejection SyntaxError The requested module 'http://[::1]:5173/node_modules/.vite/deps/lucide-react.js?v=80113ab5' doesn't provide an export named: 'UserEdit' null {"url":"http://cigi-global.test/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T13:59:17.520Z"} 
[2025-08-21 13:59:19] local.ERROR: Unhandled Promise Rejection SyntaxError The requested module 'http://[::1]:5173/node_modules/.vite/deps/lucide-react.js?v=80113ab5' doesn't provide an export named: 'UserEdit' null {"url":"http://cigi-global.test/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T13:59:19.682Z"} 
[2025-08-21 14:07:22] local.ERROR: Unhandled Promise Rejection SyntaxError The requested module 'http://[::1]:5173/node_modules/.vite/deps/lucide-react.js?v=80113ab5' doesn't provide an export named: 'UserEdit' null {"url":"http://cigi-global.test/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T14:07:21.946Z"} 
[2025-08-21 14:08:11] local.ERROR: Unhandled Promise Rejection SyntaxError The requested module 'http://[::1]:5173/node_modules/.vite/deps/lucide-react.js?v=80113ab5' doesn't provide an export named: 'UserEdit' null {"url":"http://cigi-global.test/admin/users","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T14:08:11.772Z"} 
[2025-08-21 14:15:21] local.ERROR: ReferenceError: Mail is not defined http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1755785719683 69 7 ReferenceError Mail is not defined AppSidebar@http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1755785719683:69:7
react_stack_bottom_frame@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:17424:20
renderWithHooks@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:4206:42
updateFunctionComponent@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:6619:21
beginWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:7654:20
runWithFiberInDEV@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:1487:15
performUnitOfWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10868:98
workLoopSync@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10728:60
renderRootSync@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10711:13
performWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10359:46
performSyncWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11635:26
flushSyncWorkAcrossRoots_impl@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11536:122
flushSyncWork$1@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10567:86
scheduleRefresh@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:372:11
renderer_attach/p.scheduleRefresh@<anonymous code>:1:88621
performReactRefresh/<@http://[::1]:5173/@react-refresh:228:17
performReactRefresh@http://[::1]:5173/@react-refresh:217:26
enqueueUpdate<@http://[::1]:5173/@react-refresh:608:3 {"url":"http://cigi-global.test/admin","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T14:15:20.312Z"} 
[2025-08-21 14:15:21] local.ERROR: ReferenceError: Mail is not defined http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1755785719683 69 7 ReferenceError Mail is not defined AppSidebar@http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1755785719683:69:7
react_stack_bottom_frame@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:17424:20
renderWithHooks@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:4206:42
updateFunctionComponent@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:6619:21
beginWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:7654:20
runWithFiberInDEV@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:1487:15
performUnitOfWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10868:98
workLoopSync@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10728:60
renderRootSync@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10711:13
performWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10359:46
performSyncWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11635:26
flushSyncWorkAcrossRoots_impl@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11536:122
flushSyncWork$1@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10567:86
scheduleRefresh@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:372:11
renderer_attach/p.scheduleRefresh@<anonymous code>:1:88621
performReactRefresh/<@http://[::1]:5173/@react-refresh:228:17
performReactRefresh@http://[::1]:5173/@react-refresh:217:26
enqueueUpdate<@http://[::1]:5173/@react-refresh:608:3 {"url":"http://cigi-global.test/admin","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T14:15:20.312Z"} 
[2025-08-21 14:15:21] local.WARNING: %s

%s An error occurred in the <AppSidebar> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://cigi-global.test/admin","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T14:15:20.313Z"} 
[2025-08-21 14:19:58] local.ERROR: Unhandled Promise Rejection Error Page not found: ./pages/admin/contact-submissions/index.tsx resolvePageComponent@http://[::1]:5173/node_modules/.vite/deps/laravel-vite-plugin_inertia-helpers.js?v=80113ab5:12:9
resolve@http://[::1]:5173/resources/js/app.tsx:10:42
resolveComponent@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:14350:54
resolve@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12072:33
set@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12002:17
setPage@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:13053:17 {"url":"http://cigi-global.test/admin","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T14:19:57.981Z"} 
[2025-08-21 14:19:59] local.ERROR: Unhandled Promise Rejection Error Page not found: ./pages/admin/contact-submissions/index.tsx resolvePageComponent@http://[::1]:5173/node_modules/.vite/deps/laravel-vite-plugin_inertia-helpers.js?v=80113ab5:12:9
resolve@http://[::1]:5173/resources/js/app.tsx:10:42
resolveComponent@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:14350:54
resolve@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12072:33
set@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12002:17
setPage@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:13053:17 {"url":"http://cigi-global.test/admin","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T14:19:59.211Z"} 
[2025-08-21 14:20:17] local.ERROR: Unhandled Promise Rejection Error Page not found: ./pages/admin/contact-submissions/index.tsx resolvePageComponent@http://[::1]:5173/node_modules/.vite/deps/laravel-vite-plugin_inertia-helpers.js?v=80113ab5:12:9
resolve@http://[::1]:5173/resources/js/app.tsx:10:42
resolveComponent@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:14350:54
resolve@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12072:33
set@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12002:17
setPage@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:13053:17
async*process@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12980:16
async*handle/<@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12965:34
processNext@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12106:30
process@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12098:62
add@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12095:17
handle@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12965:19
use/<@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12710:23
promise callback*use@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:12703:32
visit@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:13409:26
onClick@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:15040:18
executeDispatch@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11736:19
runWithFiberInDEV@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:1487:15
processDispatchQueue@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11772:37
node_modules/react-dom/cjs/react-dom-client.development.js/dispatchEventForPluginEventSystem/<@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:12182:31
batchedUpdates$1@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:2628:44
dispatchEventForPluginEventSystem@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11877:25
dispatchEvent@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:14792:46
dispatchDiscreteEvent@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:14773:62
EventListener.handleEvent*addTrappedEventListener@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11842:30
listenToNativeEvent@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11801:32
node_modules/react-dom/cjs/react-dom-client.development.js/listenToAllSupportedEvents/<@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11812:92
listenToAllSupportedEvents@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11811:27
node_modules/react-dom/cjs/react-dom-client.development.js/exports.createRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:18025:35
setup@http://[::1]:5173/resources/js/app.tsx:12:28
createInertiaApp/reactApp<@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:14357:12
promise callback*createInertiaApp@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:14356:6
@http://[::1]:5173/resources/js/app.tsx:8:17 {"url":"http://cigi-global.test/admin","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T14:20:17.471Z"} 
[2025-08-21 14:23:39] local.ERROR: Error: A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder. http://[::1]:5173/node_modules/.vite/deps/@radix-ui_react-select.js?v=80113ab5 869 13 Error A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder. SelectItem<@http://[::1]:5173/node_modules/.vite/deps/@radix-ui_react-select.js?v=80113ab5:869:13
react_stack_bottom_frame@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:17424:20
renderWithHooks@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:4206:42
updateForwardRef@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:6461:21
beginWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:7864:20
runWithFiberInDEV@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:1487:15
performUnitOfWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10868:98
workLoopSync@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10728:60
renderRootSync@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10711:13
performWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10359:46
performSyncWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11635:26
flushSyncWorkAcrossRoots_impl@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11536:122
flushSpawnedWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11254:40
commitRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11081:11
commitRootWhenReady@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10512:19
performWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10457:36
performWorkOnRootViaSchedulerTask@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11623:26
performWorkUntilDeadline@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:36:58
EventHandlerNonNull*node_modules/scheduler/cjs/scheduler.development.js/<@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:156:9
node_modules/scheduler/cjs/scheduler.development.js@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:266:7
__require@http://[::1]:5173/node_modules/.vite/deps/chunk-G3PMV62Z.js?v=80113ab5:8:50
node_modules/scheduler/index.js@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:277:24
__require@http://[::1]:5173/node_modules/.vite/deps/chunk-G3PMV62Z.js?v=80113ab5:8:50
node_modules/react-dom/cjs/react-dom-client.development.js/<@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:15216:23
node_modules/react-dom/cjs/react-dom-client.development.js@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:18068:7
__require@http://[::1]:5173/node_modules/.vite/deps/chunk-G3PMV62Z.js?v=80113ab5:8:50
node_modules/react-dom/client.js@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:18079:24
__require@http://[::1]:5173/node_modules/.vite/deps/chunk-G3PMV62Z.js?v=80113ab5:8:50
@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:18083:16 {"url":"http://cigi-global.test/admin/contact-submissions","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T14:23:38.896Z"} 
[2025-08-21 14:23:39] local.ERROR: Error: A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder. http://[::1]:5173/node_modules/.vite/deps/@radix-ui_react-select.js?v=80113ab5 869 13 Error A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder. SelectItem<@http://[::1]:5173/node_modules/.vite/deps/@radix-ui_react-select.js?v=80113ab5:869:13
react_stack_bottom_frame@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:17424:20
renderWithHooks@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:4206:42
updateForwardRef@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:6461:21
beginWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:7864:20
runWithFiberInDEV@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:1487:15
performUnitOfWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10868:98
workLoopSync@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10728:60
renderRootSync@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10711:13
performWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10359:46
performSyncWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11635:26
flushSyncWorkAcrossRoots_impl@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11536:122
flushSpawnedWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11254:40
commitRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11081:11
commitRootWhenReady@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10512:19
performWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10457:36
performWorkOnRootViaSchedulerTask@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11623:26
performWorkUntilDeadline@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:36:58
EventHandlerNonNull*node_modules/scheduler/cjs/scheduler.development.js/<@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:156:9
node_modules/scheduler/cjs/scheduler.development.js@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:266:7
__require@http://[::1]:5173/node_modules/.vite/deps/chunk-G3PMV62Z.js?v=80113ab5:8:50
node_modules/scheduler/index.js@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:277:24
__require@http://[::1]:5173/node_modules/.vite/deps/chunk-G3PMV62Z.js?v=80113ab5:8:50
node_modules/react-dom/cjs/react-dom-client.development.js/<@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:15216:23
node_modules/react-dom/cjs/react-dom-client.development.js@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:18068:7
__require@http://[::1]:5173/node_modules/.vite/deps/chunk-G3PMV62Z.js?v=80113ab5:8:50
node_modules/react-dom/client.js@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:18079:24
__require@http://[::1]:5173/node_modules/.vite/deps/chunk-G3PMV62Z.js?v=80113ab5:8:50
@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:18083:16 {"url":"http://cigi-global.test/admin/contact-submissions","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T14:23:38.897Z"} 
[2025-08-21 14:23:39] local.WARNING: %s

%s An error occurred in the <SelectItem> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://cigi-global.test/admin/contact-submissions","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T14:23:38.897Z"} 
[2025-08-21 14:54:32] local.ERROR: Unhandled Promise Rejection AxiosError Request aborted handleAbort@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:5489:15 {"url":"http://cigi-global.test/admin/contact-submissions/2","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T14:54:31.876Z"} 
[2025-08-21 14:54:32] local.ERROR: Unhandled Promise Rejection AxiosError Request aborted handleAbort@http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:5489:15 {"url":"http://cigi-global.test/admin/contact-submissions/2","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T14:54:31.876Z"} 
[2025-08-21 15:01:04] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://cigi-global.test/contact","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T15:01:03.779Z"} 
[2025-08-21 15:01:04] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://cigi-global.test/admin/business-units","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T15:01:03.781Z"} 
[2025-08-21 15:37:02] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://cigi-global.test/admin/business-units","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T15:37:01.535Z"} 
[2025-08-21 15:37:02] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://cigi-global.test/contact","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T15:37:01.535Z"} 
[2025-08-21 15:38:07] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://cigi-global.test/admin/business-units","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T15:38:06.962Z"} 
[2025-08-21 15:38:07] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://cigi-global.test/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-21T15:38:06.938Z"} 
[2025-08-22 00:36:19] local.ERROR: [vite] TypeError error loading dynamically imported module: http://[::1]:5173/resources/js/pages/public/home.tsx?t=1755822978073 null {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T00:36:18.332Z"} 
[2025-08-22 00:36:19] local.ERROR: [vite] Failed to reload /resources/js/pages/public/home.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T00:36:18.337Z"} 
[2025-08-22 01:10:13] local.ERROR: Received `%s` for a non-boolean attribute `%s`.

If you want to write it to the DOM, pass a string instead: %s="%s" or %s={value.toString()}. true jsx jsx true jsx Stack null style@unknown:0:0 {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T01:10:12.659Z"} 
[2025-08-22 01:10:18] local.ERROR: Received `%s` for a non-boolean attribute `%s`.

If you want to write it to the DOM, pass a string instead: %s="%s" or %s={value.toString()}. true jsx jsx true jsx Stack null style@unknown:0:0 {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T01:10:18.340Z"} 
[2025-08-22 01:10:25] local.ERROR: [vite] TypeError error loading dynamically imported module: http://[::1]:5173/resources/js/pages/public/home.tsx?t=1755825024563 null {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T01:10:24.592Z"} 
[2025-08-22 01:10:25] local.ERROR: [vite] Failed to reload /resources/js/pages/public/home.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T01:10:24.592Z"} 
[2025-08-22 01:11:28] local.ERROR: ReferenceError: useState is not defined http://[::1]:5173/resources/js/pages/public/home.tsx?t=1755825086786 24 33 ReferenceError useState is not defined Home@http://[::1]:5173/resources/js/pages/public/home.tsx?t=1755825086786:24:33
react_stack_bottom_frame@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:17424:20
renderWithHooks@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:4206:42
updateFunctionComponent@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:6619:21
beginWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:7654:20
runWithFiberInDEV@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:1487:15
performUnitOfWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10868:98
workLoopSync@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10728:60
renderRootSync@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10711:13
performWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10359:46
performSyncWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11635:26
flushSyncWorkAcrossRoots_impl@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11536:122
flushSyncWork$1@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10567:86
scheduleRefresh@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:372:11
renderer_attach/p.scheduleRefresh@<anonymous code>:1:88621
performReactRefresh/<@http://[::1]:5173/@react-refresh:228:17
performReactRefresh@http://[::1]:5173/@react-refresh:217:26
enqueueUpdate<@http://[::1]:5173/@react-refresh:608:3 {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T01:11:27.495Z"} 
[2025-08-22 01:11:28] local.ERROR: ReferenceError: useState is not defined http://[::1]:5173/resources/js/pages/public/home.tsx?t=1755825086786 24 33 ReferenceError useState is not defined Home@http://[::1]:5173/resources/js/pages/public/home.tsx?t=1755825086786:24:33
react_stack_bottom_frame@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:17424:20
renderWithHooks@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:4206:42
updateFunctionComponent@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:6619:21
beginWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:7654:20
runWithFiberInDEV@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:1487:15
performUnitOfWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10868:98
workLoopSync@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10728:60
renderRootSync@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10711:13
performWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10359:46
performSyncWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11635:26
flushSyncWorkAcrossRoots_impl@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11536:122
flushSyncWork$1@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10567:86
scheduleRefresh@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:372:11
renderer_attach/p.scheduleRefresh@<anonymous code>:1:88621
performReactRefresh/<@http://[::1]:5173/@react-refresh:228:17
performReactRefresh@http://[::1]:5173/@react-refresh:217:26
enqueueUpdate<@http://[::1]:5173/@react-refresh:608:3 {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T01:11:27.495Z"} 
[2025-08-22 01:11:28] local.WARNING: %s

%s An error occurred in the <Home> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T01:11:27.495Z"} 
[2025-08-22 01:12:18] local.ERROR: [vite] TypeError error loading dynamically imported module: http://[::1]:5173/resources/js/pages/public/home.tsx?t=1755825138358 null {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T01:12:18.435Z"} 
[2025-08-22 01:12:18] local.ERROR: [vite] Failed to reload /resources/js/pages/public/home.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T01:12:18.436Z"} 
[2025-08-22 01:17:47] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T01:17:46.757Z"} 
[2025-08-22 01:17:47] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://cigi-global.test/admin/business-units","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T01:17:46.698Z"} 
[2025-08-22 01:18:38] local.ERROR: ReferenceError: config is not defined http://[::1]:5173/resources/js/pages/public/home.tsx?t=1755825517322 452 23 ReferenceError config is not defined Home/<.children<.children<.children<.children<.children<@http://[::1]:5173/resources/js/pages/public/home.tsx?t=1755825517322:452:23
Home@http://[::1]:5173/resources/js/pages/public/home.tsx?t=1755825517322:385:136
react_stack_bottom_frame@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:17424:20
renderWithHooks@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:4206:42
updateFunctionComponent@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:6619:21
beginWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:7654:20
runWithFiberInDEV@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:1487:15
performUnitOfWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10868:98
workLoopSync@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10728:60
renderRootSync@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10711:13
performWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10359:46
performSyncWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11635:26
flushSyncWorkAcrossRoots_impl@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11536:122
flushSyncWork$1@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10567:86
scheduleRefresh@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:372:11
renderer_attach/p.scheduleRefresh@<anonymous code>:1:88621
performReactRefresh/<@http://[::1]:5173/@react-refresh:228:17
performReactRefresh@http://[::1]:5173/@react-refresh:217:26
enqueueUpdate<@http://[::1]:5173/@react-refresh:608:3 {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T01:18:38.187Z"} 
[2025-08-22 01:18:38] local.ERROR: ReferenceError: config is not defined http://[::1]:5173/resources/js/pages/public/home.tsx?t=1755825517322 452 23 ReferenceError config is not defined Home/<.children<.children<.children<.children<.children<@http://[::1]:5173/resources/js/pages/public/home.tsx?t=1755825517322:452:23
Home@http://[::1]:5173/resources/js/pages/public/home.tsx?t=1755825517322:385:136
react_stack_bottom_frame@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:17424:20
renderWithHooks@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:4206:42
updateFunctionComponent@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:6619:21
beginWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:7654:20
runWithFiberInDEV@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:1487:15
performUnitOfWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10868:98
workLoopSync@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10728:60
renderRootSync@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10711:13
performWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10359:46
performSyncWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11635:26
flushSyncWorkAcrossRoots_impl@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11536:122
flushSyncWork$1@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10567:86
scheduleRefresh@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:372:11
renderer_attach/p.scheduleRefresh@<anonymous code>:1:88621
performReactRefresh/<@http://[::1]:5173/@react-refresh:228:17
performReactRefresh@http://[::1]:5173/@react-refresh:217:26
enqueueUpdate<@http://[::1]:5173/@react-refresh:608:3 {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T01:18:38.187Z"} 
[2025-08-22 01:18:38] local.WARNING: %s

%s An error occurred in the <Home> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T01:18:38.188Z"} 
[2025-08-22 01:21:56] local.ERROR: [vite] TypeError error loading dynamically imported module: http://[::1]:5173/resources/js/pages/public/home.tsx?t=1755825716705 null {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T01:21:56.748Z"} 
[2025-08-22 01:21:56] local.ERROR: [vite] Failed to reload /resources/js/pages/public/home.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T01:21:56.748Z"} 
[2025-08-22 01:22:23] local.ERROR: ReferenceError: getBusinessUnitImage is not defined http://[::1]:5173/resources/js/pages/public/home.tsx?t=1755825742086 396 23 ReferenceError getBusinessUnitImage is not defined Home/<.children<.children<.children<.children<.children<@http://[::1]:5173/resources/js/pages/public/home.tsx?t=1755825742086:396:23
Home@http://[::1]:5173/resources/js/pages/public/home.tsx?t=1755825742086:386:136
react_stack_bottom_frame@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:17424:20
renderWithHooks@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:4206:42
updateFunctionComponent@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:6619:21
beginWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:7654:20
runWithFiberInDEV@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:1487:15
performUnitOfWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10868:98
workLoopSync@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10728:60
renderRootSync@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10711:13
performWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10359:46
performSyncWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11635:26
flushSyncWorkAcrossRoots_impl@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11536:122
flushSyncWork$1@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10567:86
scheduleRefresh@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:372:11
renderer_attach/p.scheduleRefresh@<anonymous code>:1:88621
performReactRefresh/<@http://[::1]:5173/@react-refresh:228:17
performReactRefresh@http://[::1]:5173/@react-refresh:217:26
enqueueUpdate<@http://[::1]:5173/@react-refresh:608:3 {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T01:22:22.827Z"} 
[2025-08-22 01:22:23] local.ERROR: ReferenceError: getBusinessUnitImage is not defined http://[::1]:5173/resources/js/pages/public/home.tsx?t=1755825742086 396 23 ReferenceError getBusinessUnitImage is not defined Home/<.children<.children<.children<.children<.children<@http://[::1]:5173/resources/js/pages/public/home.tsx?t=1755825742086:396:23
Home@http://[::1]:5173/resources/js/pages/public/home.tsx?t=1755825742086:386:136
react_stack_bottom_frame@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:17424:20
renderWithHooks@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:4206:42
updateFunctionComponent@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:6619:21
beginWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:7654:20
runWithFiberInDEV@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:1487:15
performUnitOfWork@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10868:98
workLoopSync@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10728:60
renderRootSync@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10711:13
performWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10359:46
performSyncWorkOnRoot@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11635:26
flushSyncWorkAcrossRoots_impl@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:11536:122
flushSyncWork$1@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:10567:86
scheduleRefresh@http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=80113ab5:372:11
renderer_attach/p.scheduleRefresh@<anonymous code>:1:88621
performReactRefresh/<@http://[::1]:5173/@react-refresh:228:17
performReactRefresh@http://[::1]:5173/@react-refresh:217:26
enqueueUpdate<@http://[::1]:5173/@react-refresh:608:3 {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T01:22:22.827Z"} 
[2025-08-22 01:22:23] local.WARNING: %s

%s An error occurred in the <Home> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T01:22:22.828Z"} 
[2025-08-22 01:26:16] local.ERROR: [vite] TypeError error loading dynamically imported module: http://[::1]:5173/resources/js/pages/public/home.tsx?t=1755825975856 null {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T01:26:15.887Z"} 
[2025-08-22 01:26:16] local.ERROR: [vite] Failed to reload /resources/js/pages/public/home.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T01:26:15.887Z"} 
[2025-08-22 01:26:30] local.ERROR: [vite] TypeError error loading dynamically imported module: http://[::1]:5173/resources/js/pages/public/home.tsx?t=1755825990362 null {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T01:26:30.395Z"} 
[2025-08-22 01:26:30] local.ERROR: [vite] Failed to reload /resources/js/pages/public/home.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T01:26:30.395Z"} 
[2025-08-22 01:33:09] local.ERROR: Unhandled Promise Rejection Error Page not found: ./pages/public/home.tsx resolvePageComponent@http://[::1]:5174/node_modules/.vite/deps/laravel-vite-plugin_inertia-helpers.js?v=80113ab5:12:9
resolve@http://[::1]:5174/resources/js/app.tsx:10:42
resolveComponent@http://[::1]:5174/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:14350:54
createInertiaApp@http://[::1]:5174/node_modules/.vite/deps/@inertiajs_react.js?v=80113ab5:14353:5
@http://[::1]:5174/resources/js/app.tsx:8:17 {"url":"http://cigi-global.test/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0","timestamp":"2025-08-22T01:33:08.632Z"} 
