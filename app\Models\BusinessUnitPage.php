<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BusinessUnitPage extends Model
{
    use HasFactory;

    protected $fillable = [
        'business_unit_id',
        'type',
        'title',
        'slug',
        'content',
        'images',
        'meta_data',
        'seo_data',
        'is_active',
        'order',
    ];

    protected function casts(): array
    {
        return [
            'images' => 'array',
            'meta_data' => 'array',
            'seo_data' => 'array',
            'is_active' => 'boolean',
            'order' => 'integer',
        ];
    }

    public function businessUnit(): BelongsTo
    {
        return $this->belongsTo(BusinessUnit::class);
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByType($query, string $type)
    {
        return $query->where('type', $type);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('order');
    }

    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    public function getMetaTitle(): ?string
    {
        return $this->seo_data['title'] ?? $this->title;
    }

    public function getMetaDescription(): ?string
    {
        return $this->seo_data['description'] ?? null;
    }

    public function getMetaKeywords(): ?string
    {
        return $this->seo_data['keywords'] ?? null;
    }
}
