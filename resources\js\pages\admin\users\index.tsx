import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, router } from '@inertiajs/react';
import { Edit, Eye, Plus, Search, Trash2, Users } from 'lucide-react';
import { useState } from 'react';

interface User {
    id: number;
    name: string;
    email: string;
    email_verified_at: string | null;
    roles: string[];
    created_at: string;
    updated_at: string;
}

interface Role {
    id: number;
    name: string;
}

interface PaginatedUsers {
    data: User[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    links: Array<{
        url: string | null;
        label: string;
        active: boolean;
    }>;
}

interface Props {
    users: PaginatedUsers;
    roles: Role[];
    search: string;
    role_filter: string;
}

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Admin', href: '/admin' },
    { title: 'Kelola Pengguna', href: '/admin/users' },
];

export default function UsersIndex({ users, roles, search, role_filter }: Props) {
    const [searchTerm, setSearchTerm] = useState(search);
    const [roleFilter, setRoleFilter] = useState(role_filter === '' ? 'all' : role_filter);

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();
        router.get(route('admin.users.index'), { search: searchTerm, role: roleFilter === 'all' ? '' : roleFilter }, { preserveState: true });
    };

    const handleRoleFilter = (value: string) => {
        setRoleFilter(value);
        router.get(route('admin.users.index'), { search: searchTerm, role: value === 'all' ? '' : value }, { preserveState: true });
    };

    const handleDelete = (user: User) => {
        if (confirm(`Apakah Anda yakin ingin menghapus pengguna ${user.name}?`)) {
            router.delete(route('admin.users.destroy', user.id));
        }
    };

    const getRoleBadgeVariant = (role: string) => {
        switch (role) {
            case 'super-admin':
                return 'destructive';
            case 'admin':
                return 'default';
            default:
                return 'secondary';
        }
    };

    const getRoleLabel = (role: string) => {
        switch (role) {
            case 'super-admin':
                return 'Super Admin';
            case 'admin':
                return 'Admin';
            default:
                return role;
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Kelola Pengguna" />

            <div className="flex h-full flex-1 flex-col gap-4 p-4">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold tracking-tight">Kelola Pengguna</h1>
                        <p className="text-muted-foreground">Kelola pengguna dan hak akses sistem</p>
                    </div>
                    <Link href="/admin/users/create">
                        <Button>
                            <Plus className="mr-2 h-4 w-4" />
                            Tambah Pengguna
                        </Button>
                    </Link>
                </div>

                {/* Search and Filter */}
                <Card>
                    <CardContent className="pt-6">
                        <form onSubmit={handleSearch} className="flex gap-2">
                            <Input
                                placeholder="Cari pengguna..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                className="flex-1"
                            />
                            <Select value={roleFilter} onValueChange={handleRoleFilter}>
                                <SelectTrigger className="w-48">
                                    <SelectValue placeholder="Filter Role" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">Semua Role</SelectItem>
                                    {roles.map((role) => (
                                        <SelectItem key={role.id} value={role.name}>
                                            {getRoleLabel(role.name)}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                            <Button type="submit" variant="outline">
                                <Search className="h-4 w-4" />
                            </Button>
                        </form>
                    </CardContent>
                </Card>

                {/* Users Table */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Users className="h-5 w-5" />
                            Daftar Pengguna
                        </CardTitle>
                        <CardDescription>Total {users.total} pengguna</CardDescription>
                    </CardHeader>
                    <CardContent>
                        {users.data.length > 0 ? (
                            <div className="space-y-4">
                                {users.data.map((user) => (
                                    <div key={user.id} className="flex items-center justify-between rounded-lg border p-4">
                                        <div className="flex-1">
                                            <div className="mb-2 flex items-center gap-3">
                                                <h3 className="font-medium">{user.name}</h3>
                                                <div className="flex gap-1">
                                                    {user.roles.map((role) => (
                                                        <Badge key={role} variant={getRoleBadgeVariant(role)}>
                                                            {getRoleLabel(role)}
                                                        </Badge>
                                                    ))}
                                                </div>
                                                {!user.email_verified_at && <Badge variant="outline">Belum Verifikasi</Badge>}
                                            </div>
                                            <p className="text-sm text-muted-foreground">{user.email}</p>
                                            <p className="text-xs text-muted-foreground">
                                                Dibuat: {new Date(user.created_at).toLocaleDateString('id-ID')}
                                            </p>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <Link href={route('admin.users.show', user.id)}>
                                                <Button variant="outline" size="sm">
                                                    <Eye className="h-4 w-4" />
                                                </Button>
                                            </Link>
                                            <Link href={route('admin.users.edit', user.id)}>
                                                <Button variant="outline" size="sm">
                                                    <Edit className="h-4 w-4" />
                                                </Button>
                                            </Link>
                                            {!user.roles.includes('super-admin') && (
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => handleDelete(user)}
                                                    className="text-destructive hover:text-destructive"
                                                >
                                                    <Trash2 className="h-4 w-4" />
                                                </Button>
                                            )}
                                        </div>
                                    </div>
                                ))}

                                {/* Pagination */}
                                {users.last_page > 1 && (
                                    <div className="mt-6 flex justify-center">
                                        <div className="flex items-center gap-2">
                                            {users.links.map((link, index) => (
                                                <Button
                                                    key={index}
                                                    variant={link.active ? 'default' : 'outline'}
                                                    size="sm"
                                                    disabled={!link.url}
                                                    onClick={() => link.url && router.get(link.url)}
                                                    dangerouslySetInnerHTML={{ __html: link.label }}
                                                />
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </div>
                        ) : (
                            <div className="py-8 text-center">
                                <Users className="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
                                <h3 className="mb-2 text-lg font-medium text-muted-foreground">
                                    {search || (role_filter && role_filter !== 'all') ? 'Tidak ada hasil' : 'Belum ada pengguna'}
                                </h3>
                                <p className="mb-4 text-sm text-muted-foreground">
                                    {search || (role_filter && role_filter !== 'all')
                                        ? 'Coba ubah filter pencarian Anda'
                                        : 'Mulai dengan menambahkan pengguna baru'}
                                </p>
                                {!search && (!role_filter || role_filter === 'all') && (
                                    <Link href="/admin/users/create">
                                        <Button>
                                            <Plus className="mr-2 h-4 w-4" />
                                            Tambah Pengguna Pertama
                                        </Button>
                                    </Link>
                                )}
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
