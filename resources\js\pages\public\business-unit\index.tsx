import GalleryLightbox from '@/components/gallery-lightbox';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Head, Link } from '@inertiajs/react';
import { ArrowRight, Building2, ExternalLink, Globe, Images, Mail, MapPin, Phone } from 'lucide-react';

interface BusinessUnit {
    id: number;
    name: string;
    slug: string;
    description: string;
    status: string;
    primary_color?: string;
    contact_info?: {
        phone?: string;
        email?: string;
        address?: string;
    };
    social_media?: {
        instagram?: string;
        facebook?: string;
    };
}

interface BusinessUnitPage {
    id: number;
    type: string;
    title: string;
    slug: string;
    content: string;
    meta_data?: Record<string, unknown>;
    is_active: boolean;
    order: number;
}

interface GalleryImage {
    id: number;
    name: string;
    url: string;
    thumb_url: string;
    preview_url: string;
}

interface Props {
    business_unit: BusinessUnit;
    hero_page?: BusinessUnitPage;
    pages: BusinessUnitPage[];
    gallery_images: GalleryImage[];
}

export default function BusinessUnitIndex({ business_unit, hero_page, pages, gallery_images }: Props) {
    const getPageUrl = (page: BusinessUnitPage) => {
        if (page.type === 'hero') return `/${business_unit.slug}`;
        return `/${business_unit.slug}/${page.slug}`;
    };

    return (
        <>
            <Head title={hero_page?.title || business_unit.name}>
                <meta name="description" content={business_unit.description} />
            </Head>

            <div className="min-h-screen bg-gray-50">
                {/* Header */}
                <header className="bg-white shadow-sm">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="flex items-center justify-between py-6">
                            <div className="flex items-center">
                                <Link href="/" className="flex items-center">
                                    <Building2 className="mr-2 h-6 w-6 text-blue-600" />
                                    <span className="text-lg font-semibold text-gray-900">Cigi Global</span>
                                </Link>
                                <span className="mx-3 text-gray-400">/</span>
                                <div className="flex items-center">
                                    {business_unit.primary_color && (
                                        <div className="mr-2 h-4 w-4 rounded-full" style={{ backgroundColor: business_unit.primary_color }} />
                                    )}
                                    <h1 className="text-lg font-semibold text-gray-900">{business_unit.name}</h1>
                                </div>
                            </div>
                            <nav className="hidden space-x-6 md:flex">
                                {pages
                                    .filter((page) => page.type !== 'hero')
                                    .map((page) => (
                                        <Link key={page.id} href={getPageUrl(page)} className="text-gray-700 hover:text-blue-600">
                                            {page.title}
                                        </Link>
                                    ))}
                            </nav>
                        </div>
                    </div>
                </header>

                {/* Hero Section */}
                {hero_page && (
                    <section
                        className="py-20 text-white"
                        style={{
                            backgroundColor: business_unit.primary_color || '#3B82F6',
                            background: `linear-gradient(135deg, ${business_unit.primary_color || '#3B82F6'}, ${business_unit.primary_color ? business_unit.primary_color + '80' : '#1E40AF'})`,
                        }}
                    >
                        <div className="mx-auto max-w-7xl px-4 text-center sm:px-6 lg:px-8">
                            <h1 className="mb-6 text-4xl font-bold md:text-6xl">{hero_page.title}</h1>
                            <p className="mx-auto mb-8 max-w-3xl text-xl opacity-90">{hero_page.content || business_unit.description}</p>
                            <div className="flex flex-col justify-center gap-4 sm:flex-row">
                                {pages.find((p) => p.type === 'about') && (
                                    <Link href={`/${business_unit.slug}/about`}>
                                        <Button size="lg" variant="secondary">
                                            Tentang Kami
                                            <ArrowRight className="ml-2 h-4 w-4" />
                                        </Button>
                                    </Link>
                                )}
                                {pages.find((p) => p.type === 'contact') && (
                                    <Link href={`/${business_unit.slug}/contact`}>
                                        <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-gray-900">
                                            Hubungi Kami
                                        </Button>
                                    </Link>
                                )}
                            </div>
                        </div>
                    </section>
                )}

                {/* Quick Info Section */}
                <section className="bg-white py-16">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
                            {/* Description */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Tentang {business_unit.name}</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <CardDescription className="text-base">{business_unit.description}</CardDescription>
                                    {pages.find((p) => p.type === 'about') && (
                                        <Link href={`/${business_unit.slug}/about`} className="mt-4 inline-block">
                                            <Button variant="outline" size="sm">
                                                Selengkapnya
                                                <ArrowRight className="ml-2 h-3 w-3" />
                                            </Button>
                                        </Link>
                                    )}
                                </CardContent>
                            </Card>

                            {/* Contact Info */}
                            {business_unit.contact_info && (
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Informasi Kontak</CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-3">
                                        {business_unit.contact_info.phone && (
                                            <div className="flex items-center gap-3">
                                                <Phone className="h-4 w-4 text-muted-foreground" />
                                                <span className="text-sm">{business_unit.contact_info.phone}</span>
                                            </div>
                                        )}
                                        {business_unit.contact_info.email && (
                                            <div className="flex items-center gap-3">
                                                <Mail className="h-4 w-4 text-muted-foreground" />
                                                <span className="text-sm">{business_unit.contact_info.email}</span>
                                            </div>
                                        )}
                                        {business_unit.contact_info.address && (
                                            <div className="flex items-start gap-3">
                                                <MapPin className="mt-0.5 h-4 w-4 text-muted-foreground" />
                                                <span className="text-sm">{business_unit.contact_info.address}</span>
                                            </div>
                                        )}
                                        {pages.find((p) => p.type === 'contact') && (
                                            <Link href={`/${business_unit.slug}/contact`} className="mt-4 inline-block">
                                                <Button variant="outline" size="sm">
                                                    Hubungi Kami
                                                    <ArrowRight className="ml-2 h-3 w-3" />
                                                </Button>
                                            </Link>
                                        )}
                                    </CardContent>
                                </Card>
                            )}

                            {/* Social Media */}
                            {business_unit.social_media && (
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Media Sosial</CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-3">
                                        {business_unit.social_media.instagram && (
                                            <div className="flex items-center gap-3">
                                                <Globe className="h-4 w-4 text-muted-foreground" />
                                                <span className="text-sm">Instagram: {business_unit.social_media.instagram}</span>
                                            </div>
                                        )}
                                        {business_unit.social_media.facebook && (
                                            <div className="flex items-center gap-3">
                                                <Globe className="h-4 w-4 text-muted-foreground" />
                                                <span className="text-sm">Facebook: {business_unit.social_media.facebook}</span>
                                            </div>
                                        )}
                                    </CardContent>
                                </Card>
                            )}
                        </div>
                    </div>
                </section>

                {/* Special Pages Section */}
                {pages.filter((p) => ['pricing', 'catalog', 'menu'].includes(p.type)).length > 0 && (
                    <section className="bg-gray-50 py-16">
                        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                            <h2 className="mb-8 text-center text-3xl font-bold text-gray-900">Layanan Kami</h2>
                            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                                {pages
                                    .filter((p) => ['pricing', 'catalog', 'menu'].includes(p.type))
                                    .map((page) => (
                                        <Card key={page.id} className="transition-shadow hover:shadow-lg">
                                            <CardHeader>
                                                <CardTitle>{page.title}</CardTitle>
                                            </CardHeader>
                                            <CardContent>
                                                <CardDescription className="mb-4">
                                                    {page.content ? page.content.substring(0, 150) + '...' : 'Lihat detail lengkap'}
                                                </CardDescription>
                                                <Link href={getPageUrl(page)}>
                                                    <Button variant="outline" size="sm">
                                                        <ExternalLink className="mr-2 h-3 w-3" />
                                                        Lihat Detail
                                                    </Button>
                                                </Link>
                                            </CardContent>
                                        </Card>
                                    ))}
                            </div>
                        </div>
                    </section>
                )}

                {/* Gallery Section */}
                {gallery_images.length > 0 && (
                    <section className="bg-white py-16">
                        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                            <div className="mb-8 flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                    <Images className="h-6 w-6 text-gray-600" />
                                    <h2 className="text-3xl font-bold text-gray-900">Galeri</h2>
                                </div>
                                <p className="text-sm text-muted-foreground">{gallery_images.length} foto</p>
                            </div>
                            <GalleryLightbox images={gallery_images.slice(0, 6)} />
                            {gallery_images.length > 6 && (
                                <div className="mt-8 text-center">
                                    <p className="text-sm text-muted-foreground">Menampilkan 6 dari {gallery_images.length} foto</p>
                                </div>
                            )}
                        </div>
                    </section>
                )}

                {/* Footer */}
                <footer className="bg-gray-900 py-12 text-white">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="text-center">
                            <div className="mb-4 flex items-center justify-center">
                                {business_unit.primary_color && (
                                    <div className="mr-3 h-6 w-6 rounded-full" style={{ backgroundColor: business_unit.primary_color }} />
                                )}
                                <h3 className="text-xl font-bold">{business_unit.name}</h3>
                            </div>
                            <p className="mx-auto mb-6 max-w-2xl text-gray-400">{business_unit.description}</p>
                            <div className="flex justify-center space-x-6">
                                <Link href="/" className="text-gray-400 hover:text-white">
                                    Kembali ke Cigi Global
                                </Link>
                                {pages.find((p) => p.type === 'contact') && (
                                    <Link href={`/${business_unit.slug}/contact`} className="text-gray-400 hover:text-white">
                                        Hubungi Kami
                                    </Link>
                                )}
                            </div>
                        </div>
                    </div>
                </footer>
            </div>
        </>
    );
}
