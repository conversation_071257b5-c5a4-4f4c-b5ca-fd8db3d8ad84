import { Button } from '@/components/ui/button';
import { <PERSON>, <PERSON><PERSON>ontent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Head, Link } from '@inertiajs/react';
import { ArrowLeft, Building2, Calendar, User } from 'lucide-react';

interface NewsArticle {
    id: number;
    title: string;
    slug: string;
    content: string;
    excerpt?: string;
    featured_image?: string;
    published_at: string;
    created_at: string;
    author: {
        name: string;
    };
    seo_data?: {
        title?: string;
        description?: string;
        keywords?: string;
    };
}

interface RelatedArticle {
    id: number;
    title: string;
    slug: string;
    excerpt?: string;
    featured_image?: string;
    published_at: string;
    author: {
        name: string;
    };
}

interface Props {
    article: NewsArticle;
    related_articles: RelatedArticle[];
}

export default function NewsShow({ article, related_articles }: Props) {
    return (
        <>
            <Head title={article.seo_data?.title || article.title}>
                <meta name="description" content={article.seo_data?.description || article.excerpt || ''} />
                {article.seo_data?.keywords && <meta name="keywords" content={article.seo_data.keywords} />}
                <meta property="og:title" content={article.title} />
                <meta property="og:description" content={article.excerpt || ''} />
                {article.featured_image && <meta property="og:image" content={article.featured_image} />}
                <meta property="og:type" content="article" />
                <meta property="article:published_time" content={article.published_at} />
                <meta property="article:author" content={article.author.name} />
            </Head>

            <div className="min-h-screen bg-gray-50">
                {/* Header */}
                <header className="bg-white shadow-sm">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="flex items-center justify-between py-6">
                            <div className="flex items-center">
                                <Link href="/" className="flex items-center">
                                    <Building2 className="mr-2 h-6 w-6 text-blue-600" />
                                    <span className="text-lg font-semibold text-gray-900">Cigi Global</span>
                                </Link>
                                <span className="mx-3 text-gray-400">/</span>
                                <Link href="/news" className="text-gray-700 hover:text-blue-600">
                                    Berita
                                </Link>
                                <span className="mx-3 text-gray-400">/</span>
                                <span className="max-w-xs truncate text-lg text-gray-600">{article.title}</span>
                            </div>
                            <nav className="hidden space-x-6 md:flex">
                                <Link href="/" className="text-gray-700 hover:text-blue-600">
                                    Beranda
                                </Link>
                                <Link href="/news" className="text-gray-700 hover:text-blue-600">
                                    Berita
                                </Link>
                                <Link href="/login" className="text-gray-700 hover:text-blue-600">
                                    Login
                                </Link>
                            </nav>
                        </div>
                    </div>
                </header>

                {/* Article Content */}
                <article className="py-8">
                    <div className="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
                        {/* Back Button */}
                        <div className="mb-6">
                            <Link href="/news">
                                <Button variant="outline" size="sm">
                                    <ArrowLeft className="mr-2 h-4 w-4" />
                                    Kembali ke Berita
                                </Button>
                            </Link>
                        </div>

                        {/* Article Header */}
                        <header className="mb-8">
                            <h1 className="mb-4 text-3xl leading-tight font-bold text-gray-900 md:text-4xl">{article.title}</h1>

                            <div className="mb-6 flex items-center gap-6 text-gray-600">
                                <div className="flex items-center gap-2">
                                    <Calendar className="h-4 w-4" />
                                    <span>
                                        {new Date(article.published_at).toLocaleDateString('id-ID', {
                                            year: 'numeric',
                                            month: 'long',
                                            day: 'numeric',
                                            hour: '2-digit',
                                            minute: '2-digit',
                                        })}
                                    </span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <User className="h-4 w-4" />
                                    <span>oleh {article.author.name}</span>
                                </div>
                            </div>

                            {article.excerpt && <p className="mb-6 text-lg leading-relaxed font-medium text-gray-700">{article.excerpt}</p>}
                        </header>

                        {/* Featured Image */}
                        {article.featured_image && (
                            <div className="mb-8">
                                <img src={article.featured_image} alt={article.title} className="h-auto w-full rounded-lg shadow-lg" />
                            </div>
                        )}

                        {/* Article Content */}
                        <div className="prose prose-lg mb-12 max-w-none">
                            <div dangerouslySetInnerHTML={{ __html: article.content }} className="leading-relaxed text-gray-700" />
                        </div>

                        {/* Article Footer */}
                        <footer className="mb-12 border-t pt-6">
                            <div className="flex items-center justify-between">
                                <div className="text-sm text-gray-600">
                                    <p>
                                        Dipublikasi pada{' '}
                                        {new Date(article.published_at).toLocaleDateString('id-ID', {
                                            year: 'numeric',
                                            month: 'long',
                                            day: 'numeric',
                                        })}
                                    </p>
                                    <p>oleh {article.author.name}</p>
                                </div>
                                <div className="flex gap-2">
                                    <Link href="/news">
                                        <Button variant="outline" size="sm">
                                            Lihat Berita Lain
                                        </Button>
                                    </Link>
                                </div>
                            </div>
                        </footer>
                    </div>
                </article>

                {/* Related Articles */}
                {related_articles.length > 0 && (
                    <section className="bg-white py-16">
                        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                            <h2 className="mb-8 text-2xl font-bold text-gray-900">Berita Terkait</h2>
                            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                                {related_articles.map((relatedArticle) => (
                                    <Card key={relatedArticle.id} className="transition-shadow hover:shadow-lg">
                                        {relatedArticle.featured_image && (
                                            <div className="aspect-video overflow-hidden rounded-t-lg">
                                                <img
                                                    src={relatedArticle.featured_image}
                                                    alt={relatedArticle.title}
                                                    className="h-full w-full object-cover transition-transform duration-300 hover:scale-105"
                                                />
                                            </div>
                                        )}
                                        <CardHeader>
                                            <CardTitle className="line-clamp-2 text-base hover:text-blue-600">
                                                <Link href={`/news/${relatedArticle.slug}`}>{relatedArticle.title}</Link>
                                            </CardTitle>
                                            <div className="flex items-center gap-2 text-xs text-gray-600">
                                                <Calendar className="h-3 w-3" />
                                                <span>{new Date(relatedArticle.published_at).toLocaleDateString('id-ID')}</span>
                                            </div>
                                        </CardHeader>
                                        <CardContent>
                                            {relatedArticle.excerpt && (
                                                <CardDescription className="mb-3 line-clamp-2 text-sm">{relatedArticle.excerpt}</CardDescription>
                                            )}
                                            <Link href={`/news/${relatedArticle.slug}`}>
                                                <Button variant="outline" size="sm">
                                                    Baca
                                                </Button>
                                            </Link>
                                        </CardContent>
                                    </Card>
                                ))}
                            </div>
                        </div>
                    </section>
                )}

                {/* Footer */}
                <footer className="bg-gray-900 py-12 text-white">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="text-center">
                            <div className="mb-4 flex items-center justify-center">
                                <Building2 className="mr-2 h-6 w-6" />
                                <h3 className="text-xl font-bold">Cigi Global</h3>
                            </div>
                            <p className="mx-auto mb-6 max-w-2xl text-gray-400">
                                Pusat bisnis terpadu yang mengembangkan berbagai unit usaha untuk kemajuan masyarakat desa.
                            </p>
                            <div className="flex justify-center space-x-6">
                                <Link href="/" className="text-gray-400 hover:text-white">
                                    Beranda
                                </Link>
                                <Link href="/news" className="text-gray-400 hover:text-white">
                                    Berita
                                </Link>
                                <Link href="/login" className="text-gray-400 hover:text-white">
                                    Login
                                </Link>
                            </div>
                        </div>
                    </div>
                </footer>
            </div>
        </>
    );
}
