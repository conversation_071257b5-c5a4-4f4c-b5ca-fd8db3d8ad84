<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\BusinessUnit;
use App\Models\ContactSubmission;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class ContactSubmissionController extends Controller
{
    use AuthorizesRequests;

    public function index(Request $request): Response
    {
        $this->authorize('view business units');

        $query = ContactSubmission::with(['businessUnit'])
            ->latest();

        // Filter by business unit
        if ($request->filled('business_unit')) {
            $businessUnitFilter = $request->get('business_unit');
            if ($businessUnitFilter === 'global') {
                $query->whereNull('business_unit_id');
            } elseif ($businessUnitFilter !== 'all') {
                $query->where('business_unit_id', $businessUnitFilter);
            }
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%")
                    ->orWhere('subject', 'like', "%{$search}%");
            });
        }

        $submissions = $query->paginate(15)->withQueryString();

        // Transform submissions data
        $submissions->getCollection()->transform(function ($submission) {
            return [
                'id' => $submission->id,
                'name' => $submission->name,
                'email' => $submission->email,
                'phone' => $submission->phone,
                'subject' => $submission->subject,
                'message' => $submission->message,
                'status' => $submission->status,
                'business_unit' => $submission->businessUnit ? [
                    'id' => $submission->businessUnit->id,
                    'name' => $submission->businessUnit->name,
                    'slug' => $submission->businessUnit->slug,
                ] : [
                    'id' => null,
                    'name' => 'Cigi Global',
                    'slug' => 'global',
                ],
                'created_at' => $submission->created_at,
                'read_at' => $submission->read_at,
                'replied_at' => $submission->replied_at,
            ];
        });

        $businessUnits = BusinessUnit::orderBy('name')->get(['id', 'name']);

        return Inertia::render('admin/contact-submissions/index', [
            'submissions' => $submissions,
            'business_units' => $businessUnits,
            'search' => $request->get('search', ''),
            'business_unit_filter' => $request->get('business_unit', ''),
            'status_filter' => $request->get('status', ''),
        ]);
    }

    public function show(ContactSubmission $contactSubmission): Response
    {
        $this->authorize('view business units');

        $contactSubmission->load('businessUnit');

        // Mark as read if it's new
        if ($contactSubmission->status === 'new') {
            $contactSubmission->markAsRead();
        }

        return Inertia::render('admin/contact-submissions/show', [
            'submission' => [
                'id' => $contactSubmission->id,
                'name' => $contactSubmission->name,
                'email' => $contactSubmission->email,
                'phone' => $contactSubmission->phone,
                'subject' => $contactSubmission->subject,
                'message' => $contactSubmission->message,
                'status' => $contactSubmission->status,
                'metadata' => $contactSubmission->metadata,
                'business_unit' => $contactSubmission->businessUnit ? [
                    'id' => $contactSubmission->businessUnit->id,
                    'name' => $contactSubmission->businessUnit->name,
                    'slug' => $contactSubmission->businessUnit->slug,
                ] : [
                    'id' => null,
                    'name' => 'Cigi Global',
                    'slug' => 'global',
                ],
                'created_at' => $contactSubmission->created_at,
                'read_at' => $contactSubmission->read_at,
                'replied_at' => $contactSubmission->replied_at,
            ],
        ]);
    }

    public function markAsReplied(ContactSubmission $contactSubmission)
    {
        $this->authorize('edit business units');

        $contactSubmission->markAsReplied();

        return back()->with('success', 'Pesan telah ditandai sebagai dibalas.');
    }
}
