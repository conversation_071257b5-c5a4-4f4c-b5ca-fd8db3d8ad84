<?php

namespace App\Http\Controllers\Public;

use App\Http\Controllers\Controller;
use App\Http\Requests\ContactFormRequest;
use App\Mail\ContactFormSubmitted;
use App\Models\BusinessUnit;
use App\Models\ContactSubmission;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Mail;
use Inertia\Inertia;
use Inertia\Response;

class BusinessUnitController extends Controller
{
    public function index(BusinessUnit $businessUnit): Response
    {
        // Load the hero page for the business unit
        $heroPage = $businessUnit->pages()
            ->where('type', 'hero')
            ->where('is_active', true)
            ->first();

        // Load other active pages for navigation
        $pages = $businessUnit->pages()
            ->where('is_active', true)
            ->orderBy('order')
            ->get();

        // Load gallery images from media library
        $galleryImages = $businessUnit->getMedia('gallery')->map(function ($media) {
            return [
                'id' => $media->id,
                'name' => $media->name,
                'url' => $media->getUrl(),
                'thumb_url' => $media->getUrl('thumb'),
                'preview_url' => $media->getUrl('preview'),
            ];
        });

        return Inertia::render('public/business-unit/index', [
            'business_unit' => $businessUnit,
            'hero_page' => $heroPage,
            'pages' => $pages,
            'gallery_images' => $galleryImages,
        ]);
    }

    public function about(BusinessUnit $businessUnit): Response
    {
        $aboutPage = $businessUnit->pages()
            ->where('type', 'about')
            ->where('is_active', true)
            ->first();

        if (! $aboutPage) {
            abort(404, 'Halaman tentang tidak ditemukan');
        }

        // Load other active pages for navigation
        $pages = $businessUnit->pages()
            ->where('is_active', true)
            ->orderBy('order')
            ->get();

        return Inertia::render('public/business-unit/about', [
            'business_unit' => $businessUnit,
            'page' => $aboutPage,
            'pages' => $pages,
        ]);
    }

    public function contact(BusinessUnit $businessUnit): Response
    {
        $contactPage = $businessUnit->pages()
            ->where('type', 'contact')
            ->where('is_active', true)
            ->first();

        if (! $contactPage) {
            abort(404, 'Halaman kontak tidak ditemukan');
        }

        // Load other active pages for navigation
        $pages = $businessUnit->pages()
            ->where('is_active', true)
            ->orderBy('order')
            ->get();

        return Inertia::render('public/business-unit/contact', [
            'business_unit' => $businessUnit,
            'page' => $contactPage,
            'pages' => $pages,
        ]);
    }

    public function gallery(BusinessUnit $businessUnit): Response
    {
        // Load all galleries for this business unit
        $galleries = $businessUnit->galleries()
            ->latest()
            ->paginate(12);

        // Load other active pages for navigation
        $pages = $businessUnit->pages()
            ->where('is_active', true)
            ->orderBy('order')
            ->get();

        return Inertia::render('public/business-unit/gallery', [
            'business_unit' => $businessUnit,
            'galleries' => $galleries,
            'pages' => $pages,
        ]);
    }

    public function pricing(BusinessUnit $businessUnit): Response
    {
        // Only show pricing for specific business units that have pricing pages
        $pricingPage = $businessUnit->pages()
            ->where('type', 'pricing')
            ->where('is_active', true)
            ->first();

        if (! $pricingPage) {
            abort(404, 'Halaman harga tidak ditemukan');
        }

        // Load other active pages for navigation
        $pages = $businessUnit->pages()
            ->where('is_active', true)
            ->orderBy('order')
            ->get();

        return Inertia::render('public/business-unit/pricing', [
            'business_unit' => $businessUnit,
            'page' => $pricingPage,
            'pages' => $pages,
        ]);
    }

    public function products(BusinessUnit $businessUnit): Response
    {
        // Only show products for specific business units that have product pages
        $productPage = $businessUnit->pages()
            ->where('type', 'catalog')
            ->where('is_active', true)
            ->first();

        if (! $productPage) {
            abort(404, 'Halaman produk tidak ditemukan');
        }

        // Load other active pages for navigation
        $pages = $businessUnit->pages()
            ->where('is_active', true)
            ->orderBy('order')
            ->get();

        return Inertia::render('public/business-unit/products', [
            'business_unit' => $businessUnit,
            'page' => $productPage,
            'pages' => $pages,
        ]);
    }

    public function menu(BusinessUnit $businessUnit): Response
    {
        // Only show menu for specific business units that have menu pages
        $menuPage = $businessUnit->pages()
            ->where('type', 'menu')
            ->where('is_active', true)
            ->first();

        if (! $menuPage) {
            abort(404, 'Halaman menu tidak ditemukan');
        }

        // Load other active pages for navigation
        $pages = $businessUnit->pages()
            ->where('is_active', true)
            ->orderBy('order')
            ->get();

        return Inertia::render('public/business-unit/menu', [
            'business_unit' => $businessUnit,
            'page' => $menuPage,
            'pages' => $pages,
        ]);
    }

    public function submitContact(ContactFormRequest $request, BusinessUnit $businessUnit): RedirectResponse
    {
        $validated = $request->validated();

        // Store the contact submission
        $submission = ContactSubmission::create([
            'business_unit_id' => $businessUnit->id,
            'name' => $validated['name'],
            'email' => $validated['email'],
            'phone' => $validated['phone'] ?? null,
            'subject' => $validated['subject'],
            'message' => $validated['message'],
            'metadata' => [
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'submitted_at' => now()->toISOString(),
            ],
        ]);

        // Send email notification
        try {
            // Determine recipient email - use business unit contact email or fallback to app default
            $recipientEmail = $businessUnit->contact_info['email'] ?? config('mail.from.address');

            Mail::to($recipientEmail)->send(new ContactFormSubmitted($submission));
        } catch (\Exception $e) {
            // Log the error but don't fail the submission
            \Log::error('Failed to send contact form email', [
                'submission_id' => $submission->id,
                'error' => $e->getMessage(),
            ]);
        }

        return back()->with('success', 'Pesan Anda telah terkirim. Kami akan segera menghubungi Anda.');
    }
}
