<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\BusinessUnit;
use App\Models\BusinessUnitPage;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Inertia\Inertia;
use Inertia\Response;

class BusinessUnitPageController extends Controller
{
    public function index(BusinessUnit $businessUnit): Response
    {
        $this->authorize('view business unit pages');

        $pages = $businessUnit->pages()
            ->ordered()
            ->get()
            ->map(function ($page) {
                return [
                    'id' => $page->id,
                    'type' => $page->type,
                    'title' => $page->title,
                    'slug' => $page->slug,
                    'is_active' => $page->is_active,
                    'order' => $page->order,
                    'created_at' => $page->created_at,
                    'updated_at' => $page->updated_at,
                ];
            });

        return Inertia::render('admin/business-units/index', [
            'business_unit' => $businessUnit,
            'pages' => $pages,
        ]);
    }

    public function create(BusinessUnit $businessUnit): Response
    {
        $this->authorize('create business unit pages');

        return Inertia::render('admin/business-units/create', [
            'business_unit' => $businessUnit,
        ]);
    }

    public function store(Request $request, BusinessUnit $businessUnit): RedirectResponse
    {
        $this->authorize('create business unit pages');

        $validated = $request->validate([
            'type' => 'required|string|in:hero,about,contact,pricing,catalog,menu,gallery,custom',
            'title' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255',
            'content' => 'nullable|string',
            'images' => 'nullable|array',
            'meta_data' => 'nullable|array',
            'seo_data' => 'nullable|array',
            'seo_data.title' => 'nullable|string|max:255',
            'seo_data.description' => 'nullable|string|max:500',
            'seo_data.keywords' => 'nullable|string',
            'is_active' => 'boolean',
            'order' => 'nullable|integer|min:0',
        ]);

        // Generate slug if not provided
        if (empty($validated['slug'])) {
            $baseSlug = Str::slug($validated['title']);
            $slug = $baseSlug;
            $counter = 1;

            while ($businessUnit->pages()->where('slug', $slug)->exists()) {
                $slug = $baseSlug.'-'.$counter;
                $counter++;
            }

            $validated['slug'] = $slug;
        }

        // Set order if not provided
        if (empty($validated['order'])) {
            $validated['order'] = $businessUnit->pages()->max('order') + 1;
        }

        $validated['business_unit_id'] = $businessUnit->id;

        BusinessUnitPage::create($validated);

        return redirect()
            ->route('admin.business-units.pages.index', $businessUnit)
            ->with('success', 'Halaman berhasil dibuat.');
    }

    public function show(BusinessUnit $businessUnit, BusinessUnitPage $page): Response
    {
        $this->authorize('view business unit pages');

        // Ensure the page belongs to the business unit
        if ($page->business_unit_id !== $businessUnit->id) {
            abort(404);
        }

        return Inertia::render('admin/business-units/show', [
            'business_unit' => $businessUnit,
            'page' => $page,
        ]);
    }

    public function edit(BusinessUnit $businessUnit, BusinessUnitPage $page): Response
    {
        $this->authorize('edit business unit pages');

        // Ensure the page belongs to the business unit
        if ($page->business_unit_id !== $businessUnit->id) {
            abort(404);
        }

        return Inertia::render('admin/business-units/edit', [
            'business_unit' => $businessUnit,
            'page' => $page,
        ]);
    }

    public function update(Request $request, BusinessUnit $businessUnit, BusinessUnitPage $page): RedirectResponse
    {
        $this->authorize('edit business unit pages');

        // Ensure the page belongs to the business unit
        if ($page->business_unit_id !== $businessUnit->id) {
            abort(404);
        }

        $validated = $request->validate([
            'type' => 'required|string|in:hero,about,contact,pricing,catalog,menu,gallery,custom',
            'title' => 'required|string|max:255',
            'slug' => 'required|string|max:255',
            'content' => 'nullable|string',
            'images' => 'nullable|array',
            'meta_data' => 'nullable|array',
            'seo_data' => 'nullable|array',
            'seo_data.title' => 'nullable|string|max:255',
            'seo_data.description' => 'nullable|string|max:500',
            'seo_data.keywords' => 'nullable|string',
            'is_active' => 'boolean',
            'order' => 'nullable|integer|min:0',
        ]);

        // Check if slug is unique within the business unit (excluding current page)
        if ($businessUnit->pages()->where('slug', $validated['slug'])->where('id', '!=', $page->id)->exists()) {
            return back()->withErrors(['slug' => 'Slug sudah digunakan untuk halaman lain di unit bisnis ini.']);
        }

        $page->update($validated);

        return redirect()
            ->route('admin.business-units.pages.index', $businessUnit)
            ->with('success', 'Halaman berhasil diperbarui.');
    }

    public function destroy(BusinessUnit $businessUnit, BusinessUnitPage $page): RedirectResponse
    {
        $this->authorize('delete business unit pages');

        // Ensure the page belongs to the business unit
        if ($page->business_unit_id !== $businessUnit->id) {
            abort(404);
        }

        $page->delete();

        return redirect()
            ->route('admin.business-units.pages.index', $businessUnit)
            ->with('success', 'Halaman berhasil dihapus.');
    }

    public function duplicate(BusinessUnit $businessUnit, BusinessUnitPage $page): RedirectResponse
    {
        $this->authorize('create business unit pages');

        // Ensure the page belongs to the business unit
        if ($page->business_unit_id !== $businessUnit->id) {
            abort(404);
        }

        $newPage = $page->replicate();
        $newPage->title = $page->title.' (Copy)';
        $newPage->slug = $page->slug.'-copy';
        $newPage->order = $businessUnit->pages()->max('order') + 1;
        $newPage->is_active = false;
        $newPage->save();

        return redirect()
            ->route('admin.business-units.pages.index', $businessUnit)
            ->with('success', 'Halaman berhasil diduplikasi.');
    }
}
