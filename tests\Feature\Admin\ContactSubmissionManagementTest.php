<?php

use App\Models\BusinessUnit;
use App\Models\ContactSubmission;
use App\Models\User;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

beforeEach(function () {
    // Run migrations
    $this->artisan('migrate:fresh');

    // Create permissions and roles
    Permission::create(['name' => 'view business units']);
    Permission::create(['name' => 'edit business units']);

    $adminRole = Role::create(['name' => 'admin']);
    $adminRole->givePermissionTo(['view business units', 'edit business units']);

    // Create admin user
    $this->admin = User::factory()->create();
    $this->admin->assignRole('admin');

    // Create business unit
    $this->businessUnit = BusinessUnit::factory()->create();

    // Create contact submissions
    $this->newSubmission = ContactSubmission::factory()->unread()->create([
        'business_unit_id' => $this->businessUnit->id,
    ]);

    $this->readSubmission = ContactSubmission::factory()->read()->create([
        'business_unit_id' => $this->businessUnit->id,
    ]);
});

it('allows admin to view contact submissions index', function () {
    $response = $this->actingAs($this->admin)
        ->get(route('admin.contact-submissions.index'));

    $response->assertStatus(200);
    $response->assertInertia(fn ($page) => $page->component('admin/contact-submissions/index'));
});

it('displays contact submissions in the index', function () {
    $response = $this->actingAs($this->admin)
        ->get(route('admin.contact-submissions.index'));

    $response->assertInertia(fn ($page) => $page->has('submissions.data', 2)
        ->where('submissions.data.0.id', $this->readSubmission->id)
        ->where('submissions.data.1.id', $this->newSubmission->id)
    );
});

it('can filter submissions by business unit', function () {
    $otherBusinessUnit = BusinessUnit::factory()->create();
    ContactSubmission::factory()->create(['business_unit_id' => $otherBusinessUnit->id]);

    $response = $this->actingAs($this->admin)
        ->get(route('admin.contact-submissions.index', ['business_unit' => $this->businessUnit->id]));

    $response->assertInertia(fn ($page) => $page->has('submissions.data', 2)
        ->where('business_unit_filter', $this->businessUnit->id)
    );
});

it('can filter submissions by status', function () {
    $response = $this->actingAs($this->admin)
        ->get(route('admin.contact-submissions.index', ['status' => 'new']));

    $response->assertInertia(fn ($page) => $page->has('submissions.data', 1)
        ->where('submissions.data.0.status', 'new')
        ->where('status_filter', 'new')
    );
});

it('can search submissions', function () {
    $response = $this->actingAs($this->admin)
        ->get(route('admin.contact-submissions.index', ['search' => $this->newSubmission->name]));

    $response->assertInertia(fn ($page) => $page->has('submissions.data', 1)
        ->where('submissions.data.0.name', $this->newSubmission->name)
        ->where('search', $this->newSubmission->name)
    );
});

it('allows admin to view individual contact submission', function () {
    $response = $this->actingAs($this->admin)
        ->get(route('admin.contact-submissions.show', $this->newSubmission));

    $response->assertStatus(200);
    $response->assertInertia(fn ($page) => $page->component('admin/contact-submissions/show')
        ->where('submission.id', $this->newSubmission->id)
        ->where('submission.name', $this->newSubmission->name)
    );
});

it('marks submission as read when viewing', function () {
    expect($this->newSubmission->status)->toBe('new');
    expect($this->newSubmission->read_at)->toBeNull();

    $this->actingAs($this->admin)
        ->get(route('admin.contact-submissions.show', $this->newSubmission));

    $this->newSubmission->refresh();
    expect($this->newSubmission->status)->toBe('read');
    expect($this->newSubmission->read_at)->not->toBeNull();
});

it('allows admin to mark submission as replied', function () {
    $response = $this->actingAs($this->admin)
        ->post(route('admin.contact-submissions.mark-replied', $this->readSubmission));

    $response->assertRedirect();
    $response->assertSessionHas('success');

    $this->readSubmission->refresh();
    expect($this->readSubmission->status)->toBe('replied');
    expect($this->readSubmission->replied_at)->not->toBeNull();
});

it('prevents unauthorized access to contact submissions', function () {
    $user = User::factory()->create(); // User without permissions

    $response = $this->actingAs($user)
        ->get(route('admin.contact-submissions.index'));

    $response->assertStatus(403);
});
