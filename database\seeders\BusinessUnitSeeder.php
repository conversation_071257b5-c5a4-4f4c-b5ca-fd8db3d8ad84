<?php

namespace Database\Seeders;

use App\Models\BusinessUnit;
use App\Models\BusinessUnitPage;
use Illuminate\Database\Seeder;

class BusinessUnitSeeder extends Seeder
{
    public function run(): void
    {
        $businessUnits = [
            [
                'name' => 'PB Cigi',
                'slug' => 'pbcigi',
                'description' => 'Tim <PERSON> Klub badminton lokal yang mengembangkan bakat olahraga di desa',
                'status' => 'active',
                'order' => 1,
                'contact_info' => [
                    'phone' => '+62 812-3456-7890',
                    'email' => '<EMAIL>',
                    'address' => 'Gedung Olahraga Desa, Cigi',
                ],
                'social_media' => [
                    'instagram' => '@pbcigi',
                    'facebook' => 'PB Cigi Official',
                ],
                'primary_color' => '#FF6B35',
            ],
            [
                'name' => 'Cigi Net',
                'slug' => 'ciginet',
                'description' => 'Penyedia layanan internet berkualitas tinggi untuk wilayah pedesaan',
                'status' => 'active',
                'order' => 2,
                'contact_info' => [
                    'phone' => '+62 813-4567-8901',
                    'email' => '<EMAIL>',
                    'address' => 'Kantor Pusat Cigi Net, Jl. Raya Desa No. 123',
                ],
                'social_media' => [
                    'instagram' => '@ciginet_official',
                    'facebook' => 'Cigi Net Internet Service',
                ],
                'primary_color' => '#4285F4',
            ],
            [
                'name' => 'Cigi Mart',
                'slug' => 'cigimart',
                'description' => 'Toko ritel modern yang menyediakan kebutuhan sehari-hari masyarakat desa',
                'status' => 'active',
                'order' => 3,
                'contact_info' => [
                    'phone' => '+62 814-5678-9012',
                    'email' => '<EMAIL>',
                    'address' => 'Jl. Pasar Desa No. 45, Cigi',
                ],
                'social_media' => [
                    'instagram' => '@cigimart',
                    'facebook' => 'Cigi Mart',
                ],
                'primary_color' => '#34A853',
            ],
            [
                'name' => 'Cigi Food',
                'slug' => 'cigifood',
                'description' => 'Usaha kuliner yang menyajikan makanan khas daerah dan modern',
                'status' => 'active',
                'order' => 4,
                'contact_info' => [
                    'phone' => '+62 815-6789-0123',
                    'email' => '<EMAIL>',
                    'address' => 'Jl. Kuliner Desa No. 67, Cigi',
                ],
                'social_media' => [
                    'instagram' => '@cigifood',
                    'facebook' => 'Cigi Food Kuliner',
                ],
                'primary_color' => '#FF9500',
            ],
            [
                'name' => 'Cigi FC',
                'slug' => 'cigifc',
                'description' => 'Tim sepak bola desa yang aktif dalam kompetisi lokal dan regional',
                'status' => 'active',
                'order' => 5,
                'contact_info' => [
                    'phone' => '+62 816-7890-1234',
                    'email' => '<EMAIL>',
                    'address' => 'Lapangan Sepak Bola Desa, Cigi',
                ],
                'social_media' => [
                    'instagram' => '@cigifc',
                    'facebook' => 'Cigi FC Official',
                ],
                'primary_color' => '#DC3545',
            ],
            [
                'name' => 'Cigi Farm',
                'slug' => 'cigifarm',
                'description' => 'Usaha pertanian modern yang menghasilkan produk berkualitas tinggi',
                'status' => 'active',
                'order' => 6,
                'contact_info' => [
                    'phone' => '+62 817-8901-2345',
                    'email' => '<EMAIL>',
                    'address' => 'Area Pertanian Desa, Cigi',
                ],
                'social_media' => [
                    'instagram' => '@cigifarm',
                    'facebook' => 'Cigi Farm Agriculture',
                ],
                'primary_color' => '#28A745',
            ],
            [
                'name' => 'Cigi Archery',
                'slug' => 'cigiarchery',
                'description' => 'Klub panahan yang mengembangkan olahraga tradisional dan modern',
                'status' => 'active',
                'order' => 7,
                'contact_info' => [
                    'phone' => '+62 818-9012-3456',
                    'email' => '<EMAIL>',
                    'address' => 'Lapangan Panahan Desa, Cigi',
                ],
                'social_media' => [
                    'instagram' => '@cigiarchery',
                    'facebook' => 'Cigi Archery Club',
                ],
                'primary_color' => '#6F42C1',
            ],
            [
                'name' => 'KRL Cigi',
                'slug' => 'krlcigi',
                'description' => 'Proyek desa ramah lingkungan yang berkelanjutan - Segera Hadir',
                'status' => 'coming_soon',
                'order' => 8,
                'contact_info' => [
                    'phone' => '+62 819-0123-4567',
                    'email' => '<EMAIL>',
                    'address' => 'Area Pengembangan Desa, Cigi',
                ],
                'social_media' => [
                    'instagram' => '@krlcigi',
                    'facebook' => 'KRL Cigi Eco Village',
                ],
                'primary_color' => '#20C997',
            ],
        ];

        foreach ($businessUnits as $unitData) {
            $unit = BusinessUnit::create($unitData);

            // Create basic pages for each business unit
            $this->createBasicPages($unit);
        }
    }

    private function createBasicPages(BusinessUnit $unit): void
    {
        // Hero page
        BusinessUnitPage::create([
            'business_unit_id' => $unit->id,
            'type' => 'hero',
            'title' => "Selamat Datang di {$unit->name}",
            'content' => $unit->description,
            'meta_data' => [
                'subtitle' => 'Unit Bisnis Cigi Global',
                'button_text' => 'Pelajari Lebih Lanjut',
                'button_link' => "/{$unit->slug}/about",
            ],
            'seo_data' => [
                'title' => "{$unit->name} - Cigi Global",
                'description' => $unit->description,
                'keywords' => "{$unit->name}, Cigi Global, {$unit->slug}",
            ],
            'is_active' => true,
            'order' => 1,
        ]);

        // About page
        BusinessUnitPage::create([
            'business_unit_id' => $unit->id,
            'type' => 'about',
            'title' => "Tentang {$unit->name}",
            'content' => '<h2>Visi</h2><p>Menjadi yang terdepan dalam bidang kami di tingkat lokal dan regional.</p><h2>Misi</h2><p>Memberikan pelayanan terbaik kepada masyarakat dengan mengutamakan kualitas dan kepuasan pelanggan.</p>',
            'seo_data' => [
                'title' => "Tentang {$unit->name} - Cigi Global",
                'description' => "Pelajari lebih lanjut tentang {$unit->name}, visi, misi, dan komitmen kami",
                'keywords' => "tentang {$unit->name}, visi misi, Cigi Global",
            ],
            'is_active' => true,
            'order' => 2,
        ]);

        // Contact page
        BusinessUnitPage::create([
            'business_unit_id' => $unit->id,
            'type' => 'contact',
            'title' => "Hubungi {$unit->name}",
            'content' => '<p>Kami siap melayani Anda. Hubungi kami melalui kontak di bawah ini:</p>',
            'meta_data' => $unit->contact_info,
            'seo_data' => [
                'title' => "Kontak {$unit->name} - Cigi Global",
                'description' => "Hubungi {$unit->name} untuk informasi dan layanan lebih lanjut",
                'keywords' => "kontak {$unit->name}, hubungi, Cigi Global",
            ],
            'is_active' => true,
            'order' => 3,
        ]);

        // Special pages for specific units
        if ($unit->slug === 'ciginet') {
            BusinessUnitPage::create([
                'business_unit_id' => $unit->id,
                'type' => 'pricing',
                'title' => 'Paket Internet Cigi Net',
                'content' => '<p>Pilih paket internet yang sesuai dengan kebutuhan Anda:</p>',
                'meta_data' => [
                    'packages' => [
                        [
                            'name' => 'Paket Rumahan',
                            'speed' => '10 Mbps',
                            'price' => 150000,
                            'features' => ['Internet Unlimited', 'WiFi Router', 'Customer Support 24/7'],
                        ],
                        [
                            'name' => 'Paket Bisnis',
                            'speed' => '25 Mbps',
                            'price' => 350000,
                            'features' => ['Internet Unlimited', 'WiFi Router', 'Static IP', 'Priority Support'],
                        ],
                        [
                            'name' => 'Paket Premium',
                            'speed' => '50 Mbps',
                            'price' => 650000,
                            'features' => ['Internet Unlimited', 'WiFi Router', 'Static IP', 'Dedicated Support', 'Backup Connection'],
                        ],
                    ],
                ],
                'is_active' => true,
                'order' => 4,
            ]);
        }
    }
}
