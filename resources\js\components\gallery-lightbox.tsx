import { Button } from '@/components/ui/button';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { ChevronLeft, ChevronRight, Download, X } from 'lucide-react';
import { useState } from 'react';

interface GalleryImage {
    id: number;
    name: string;
    url: string;
    thumb_url: string;
    preview_url: string;
}

interface Props {
    images: GalleryImage[];
    className?: string;
}

export default function GalleryLightbox({ images, className = '' }: Props) {
    const [selectedImageIndex, setSelectedImageIndex] = useState<number | null>(null);
    const [isOpen, setIsOpen] = useState(false);

    const openLightbox = (index: number) => {
        setSelectedImageIndex(index);
        setIsOpen(true);
    };

    const closeLightbox = () => {
        setIsOpen(false);
        setSelectedImageIndex(null);
    };

    const goToPrevious = () => {
        if (selectedImageIndex !== null && selectedImageIndex > 0) {
            setSelectedImageIndex(selectedImageIndex - 1);
        }
    };

    const goToNext = () => {
        if (selectedImageIndex !== null && selectedImageIndex < images.length - 1) {
            setSelectedImageIndex(selectedImageIndex + 1);
        }
    };

    const handleKeyDown = (e: React.KeyboardEvent) => {
        if (e.key === 'ArrowLeft') {
            goToPrevious();
        } else if (e.key === 'ArrowRight') {
            goToNext();
        } else if (e.key === 'Escape') {
            closeLightbox();
        }
    };

    if (images.length === 0) {
        return null;
    }

    const selectedImage = selectedImageIndex !== null ? images[selectedImageIndex] : null;

    return (
        <div className={className}>
            {/* Gallery Grid */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {images.map((image, index) => (
                    <div
                        key={image.id}
                        className="group aspect-square cursor-pointer overflow-hidden rounded-lg bg-gray-100"
                        onClick={() => openLightbox(index)}
                    >
                        <img
                            src={image.thumb_url}
                            alt={image.name}
                            className="h-full w-full object-cover transition-transform duration-300 group-hover:scale-105"
                        />
                    </div>
                ))}
            </div>

            {/* Lightbox Modal */}
            <Dialog open={isOpen} onOpenChange={setIsOpen}>
                <DialogContent className="h-full max-h-screen w-full max-w-7xl bg-black/95 p-0" onKeyDown={handleKeyDown}>
                    {selectedImage && (
                        <div className="relative flex h-full w-full items-center justify-center">
                            {/* Close Button */}
                            <Button
                                variant="ghost"
                                size="icon"
                                className="absolute top-4 right-4 z-10 text-white hover:bg-white/20"
                                onClick={closeLightbox}
                            >
                                <X className="h-6 w-6" />
                            </Button>

                            {/* Download Button */}
                            <Button variant="ghost" size="icon" className="absolute top-4 right-16 z-10 text-white hover:bg-white/20" asChild>
                                <a href={selectedImage.url} download={selectedImage.name}>
                                    <Download className="h-6 w-6" />
                                </a>
                            </Button>

                            {/* Previous Button */}
                            {selectedImageIndex !== null && selectedImageIndex > 0 && (
                                <Button
                                    variant="ghost"
                                    size="icon"
                                    className="absolute top-1/2 left-4 z-10 -translate-y-1/2 text-white hover:bg-white/20"
                                    onClick={goToPrevious}
                                >
                                    <ChevronLeft className="h-8 w-8" />
                                </Button>
                            )}

                            {/* Next Button */}
                            {selectedImageIndex !== null && selectedImageIndex < images.length - 1 && (
                                <Button
                                    variant="ghost"
                                    size="icon"
                                    className="absolute top-1/2 right-4 z-10 -translate-y-1/2 text-white hover:bg-white/20"
                                    onClick={goToNext}
                                >
                                    <ChevronRight className="h-8 w-8" />
                                </Button>
                            )}

                            {/* Main Image */}
                            <div className="flex h-full w-full items-center justify-center p-8">
                                <img src={selectedImage.preview_url} alt={selectedImage.name} className="max-h-full max-w-full object-contain" />
                            </div>

                            {/* Image Info */}
                            <div className="absolute right-4 bottom-4 left-4 z-10">
                                <div className="rounded-lg bg-black/50 p-4 text-white">
                                    <h3 className="font-medium">{selectedImage.name}</h3>
                                    <p className="text-sm text-gray-300">
                                        {selectedImageIndex !== null ? selectedImageIndex + 1 : 0} dari {images.length}
                                    </p>
                                </div>
                            </div>

                            {/* Thumbnail Navigation */}
                            <div className="absolute bottom-20 left-1/2 z-10 -translate-x-1/2">
                                <div className="flex max-w-md gap-2 overflow-x-auto rounded-lg bg-black/50 p-2">
                                    {images.map((image, index) => (
                                        <button
                                            key={image.id}
                                            className={`h-12 w-12 flex-shrink-0 overflow-hidden rounded border-2 transition-colors ${
                                                index === selectedImageIndex ? 'border-white' : 'border-transparent hover:border-gray-400'
                                            }`}
                                            onClick={() => setSelectedImageIndex(index)}
                                        >
                                            <img src={image.thumb_url} alt={image.name} className="h-full w-full object-cover" />
                                        </button>
                                    ))}
                                </div>
                            </div>
                        </div>
                    )}
                </DialogContent>
            </Dialog>
        </div>
    );
}
