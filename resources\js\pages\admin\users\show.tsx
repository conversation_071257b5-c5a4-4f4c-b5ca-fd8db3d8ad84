import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/react';
import { ArrowLeft, Calendar, Edit, Mail, Shield, User } from 'lucide-react';

interface UserData {
    id: number;
    name: string;
    email: string;
    email_verified_at: string | null;
    roles: string[];
    created_at: string;
    updated_at: string;
}

interface Props {
    user: UserData;
}

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Admin', href: '/admin' },
    { title: 'Kelola Pengguna', href: '/admin/users' },
    { title: 'Detail Pengguna', href: '#' },
];

export default function ShowUser({ user }: Props) {
    const getRoleBadgeVariant = (role: string) => {
        switch (role) {
            case 'super-admin':
                return 'destructive';
            case 'admin':
                return 'default';
            default:
                return 'secondary';
        }
    };

    const getRoleLabel = (role: string) => {
        switch (role) {
            case 'super-admin':
                return 'Super Admin';
            case 'admin':
                return 'Admin';
            default:
                return role;
        }
    };

    const getRoleDescription = (role: string) => {
        switch (role) {
            case 'super-admin':
                return 'Akses penuh ke semua fitur sistem termasuk manajemen pengguna';
            case 'admin':
                return 'Akses ke manajemen konten (unit bisnis, berita, pengaturan) tanpa manajemen pengguna';
            default:
                return 'Role khusus dengan hak akses terbatas';
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Detail Pengguna - ${user.name}`} />

            <div className="flex h-full flex-1 flex-col gap-4 p-4">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold tracking-tight">Detail Pengguna</h1>
                        <p className="text-muted-foreground">Informasi lengkap pengguna {user.name}</p>
                    </div>
                    <div className="flex items-center gap-2">
                        <Link href={route('admin.users.edit', user.id)}>
                            <Button>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit Pengguna
                            </Button>
                        </Link>
                        <Link href="/admin/users">
                            <Button variant="outline">
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Kembali
                            </Button>
                        </Link>
                    </div>
                </div>

                <div className="grid gap-4 md:grid-cols-2">
                    {/* User Information */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <User className="h-5 w-5" />
                                Informasi Pengguna
                            </CardTitle>
                            <CardDescription>Data pribadi dan kontak pengguna</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="space-y-2">
                                <div className="flex items-center gap-2">
                                    <User className="h-4 w-4 text-muted-foreground" />
                                    <span className="font-medium">Nama Lengkap</span>
                                </div>
                                <p className="text-lg">{user.name}</p>
                            </div>

                            <div className="space-y-2">
                                <div className="flex items-center gap-2">
                                    <Mail className="h-4 w-4 text-muted-foreground" />
                                    <span className="font-medium">Email</span>
                                </div>
                                <p className="text-lg">{user.email}</p>
                                <div className="flex items-center gap-2">
                                    <Badge variant={user.email_verified_at ? 'default' : 'secondary'}>
                                        {user.email_verified_at ? 'Terverifikasi' : 'Belum Verifikasi'}
                                    </Badge>
                                    {user.email_verified_at && (
                                        <span className="text-xs text-muted-foreground">
                                            pada {new Date(user.email_verified_at).toLocaleDateString('id-ID')}
                                        </span>
                                    )}
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Role Information */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Shield className="h-5 w-5" />
                                Role & Hak Akses
                            </CardTitle>
                            <CardDescription>Role dan permission yang dimiliki pengguna</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="space-y-2">
                                <span className="font-medium">Role Aktif</span>
                                <div className="flex flex-wrap gap-2">
                                    {user.roles.map((role) => (
                                        <Badge key={role} variant={getRoleBadgeVariant(role)}>
                                            {getRoleLabel(role)}
                                        </Badge>
                                    ))}
                                </div>
                            </div>

                            <div className="space-y-2">
                                <span className="font-medium">Deskripsi Hak Akses</span>
                                <div className="space-y-2">
                                    {user.roles.map((role) => (
                                        <div key={role} className="rounded-lg bg-muted p-3">
                                            <h4 className="mb-1 text-sm font-medium">{getRoleLabel(role)}</h4>
                                            <p className="text-sm text-muted-foreground">{getRoleDescription(role)}</p>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Account Activity */}
                    <Card className="md:col-span-2">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Calendar className="h-5 w-5" />
                                Aktivitas Akun
                            </CardTitle>
                            <CardDescription>Riwayat dan informasi aktivitas akun</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="grid gap-4 md:grid-cols-3">
                                <div className="space-y-2">
                                    <span className="text-sm font-medium">Akun Dibuat</span>
                                    <p className="text-sm text-muted-foreground">
                                        {new Date(user.created_at).toLocaleDateString('id-ID', {
                                            year: 'numeric',
                                            month: 'long',
                                            day: 'numeric',
                                            hour: '2-digit',
                                            minute: '2-digit',
                                        })}
                                    </p>
                                </div>

                                <div className="space-y-2">
                                    <span className="text-sm font-medium">Terakhir Diperbarui</span>
                                    <p className="text-sm text-muted-foreground">
                                        {new Date(user.updated_at).toLocaleDateString('id-ID', {
                                            year: 'numeric',
                                            month: 'long',
                                            day: 'numeric',
                                            hour: '2-digit',
                                            minute: '2-digit',
                                        })}
                                    </p>
                                </div>

                                <div className="space-y-2">
                                    <span className="text-sm font-medium">Status Akun</span>
                                    <div className="flex items-center gap-2">
                                        <Badge variant="default">Aktif</Badge>
                                        {user.roles.includes('super-admin') && <Badge variant="destructive">Protected</Badge>}
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
